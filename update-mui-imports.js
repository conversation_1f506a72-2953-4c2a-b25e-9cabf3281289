#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Common MUI components and their import paths
const componentMap = {
  // Core components
  'Box': '@mui/material/Box',
  'Typography': '@mui/material/Typography',
  'Button': '@mui/material/Button',
  'IconButton': '@mui/material/IconButton',
  'Stack': '@mui/material/Stack',
  'Paper': '@mui/material/Paper',
  'Dialog': '@mui/material/Dialog',
  'DialogTitle': '@mui/material/DialogTitle',
  'DialogContent': '@mui/material/DialogContent',
  'DialogActions': '@mui/material/DialogActions',
  'TextField': '@mui/material/TextField',
  'FormControl': '@mui/material/FormControl',
  'InputLabel': '@mui/material/InputLabel',
  'Select': '@mui/material/Select',
  'MenuItem': '@mui/material/MenuItem',
  'FormHelperText': '@mui/material/FormHelperText',
  'Table': '@mui/material/Table',
  'TableBody': '@mui/material/TableBody',
  'TableCell': '@mui/material/TableCell',
  'TableContainer': '@mui/material/TableContainer',
  'TableHead': '@mui/material/TableHead',
  'TableRow': '@mui/material/TableRow',
  'Grid': '@mui/material/Grid',
  'CircularProgress': '@mui/material/CircularProgress',
  'TablePagination': '@mui/material/TablePagination',
  'Avatar': '@mui/material/Avatar',
  'Divider': '@mui/material/Divider',
  'List': '@mui/material/List',
  'ListItem': '@mui/material/ListItem',
  'ListItemButton': '@mui/material/ListItemButton',
  'ListItemIcon': '@mui/material/ListItemIcon',
  'ListItemText': '@mui/material/ListItemText',
  'Toolbar': '@mui/material/Toolbar',
  'AppBar': '@mui/material/AppBar',
  'Drawer': '@mui/material/Drawer',
  'CssBaseline': '@mui/material/CssBaseline',
  'Chip': '@mui/material/Chip',
  'Card': '@mui/material/Card',
  'CardContent': '@mui/material/CardContent',
  'CardActions': '@mui/material/CardActions',
  'Switch': '@mui/material/Switch',
  'FormControlLabel': '@mui/material/FormControlLabel',
  'Checkbox': '@mui/material/Checkbox',
  'Radio': '@mui/material/Radio',
  'RadioGroup': '@mui/material/RadioGroup',
  'Accordion': '@mui/material/Accordion',
  'AccordionSummary': '@mui/material/AccordionSummary',
  'AccordionDetails': '@mui/material/AccordionDetails',
  'Tabs': '@mui/material/Tabs',
  'Tab': '@mui/material/Tab',
  'Tooltip': '@mui/material/Tooltip',
  'Snackbar': '@mui/material/Snackbar',
  'Alert': '@mui/material/Alert',
  'Backdrop': '@mui/material/Backdrop',
  'Modal': '@mui/material/Modal',
  'Fade': '@mui/material/Fade',
  'Slide': '@mui/material/Slide',
  'Grow': '@mui/material/Grow',
  'Collapse': '@mui/material/Collapse',
  'LinearProgress': '@mui/material/LinearProgress',
  'Skeleton': '@mui/material/Skeleton',
  'Rating': '@mui/material/Rating',
  'Slider': '@mui/material/Slider',
  'SpeedDial': '@mui/material/SpeedDial',
  'SpeedDialAction': '@mui/material/SpeedDialAction',
  'SpeedDialIcon': '@mui/material/SpeedDialIcon',
  'Stepper': '@mui/material/Stepper',
  'Step': '@mui/material/Step',
  'StepLabel': '@mui/material/StepLabel',
  'StepContent': '@mui/material/StepContent',
  'ToggleButton': '@mui/material/ToggleButton',
  'ToggleButtonGroup': '@mui/material/ToggleButtonGroup',
  'Breadcrumbs': '@mui/material/Breadcrumbs',
  'Link': '@mui/material/Link',
  'Menu': '@mui/material/Menu',
  'MenuList': '@mui/material/MenuList',
  'Popover': '@mui/material/Popover',
  'Popper': '@mui/material/Popper',
  'ClickAwayListener': '@mui/material/ClickAwayListener',
  'Portal': '@mui/material/Portal',
  'NoSsr': '@mui/material/NoSsr',
  'Hidden': '@mui/material/Hidden',
  'Container': '@mui/material/Container',
  'ImageList': '@mui/material/ImageList',
  'ImageListItem': '@mui/material/ImageListItem',
  'ImageListItemBar': '@mui/material/ImageListItemBar',
  'Masonry': '@mui/material/Masonry',
  'Timeline': '@mui/material/Timeline',
  'TimelineItem': '@mui/material/TimelineItem',
  'TimelineSeparator': '@mui/material/TimelineSeparator',
  'TimelineConnector': '@mui/material/TimelineConnector',
  'TimelineContent': '@mui/material/TimelineContent',
  'TimelineDot': '@mui/material/TimelineDot',
  'TimelineOppositeContent': '@mui/material/TimelineOppositeContent',
  'TreeView': '@mui/material/TreeView',
  'TreeItem': '@mui/material/TreeItem',
  'DataGrid': '@mui/material/DataGrid',
  'ButtonGroup': '@mui/material/ButtonGroup',
  'Fab': '@mui/material/Fab',
  
  // Types and interfaces that should come from styles
  'Theme': '@mui/material/styles',
  'ThemeProvider': '@mui/material/styles',
  'styled': '@mui/material/styles',
  'useTheme': '@mui/material/styles',
  'createTheme': '@mui/material/styles',
  'alpha': '@mui/material/styles',
  'Components': '@mui/material/styles',
  'PaletteOptions': '@mui/material/styles',
  'PaletteColorOptions': '@mui/material/styles',
  'TypographyVariantsOptions': '@mui/material/styles',
  'Shadows': '@mui/material/styles',
  'CSSObject': '@mui/material/styles',
  'SxProps': '@mui/material/styles',
  
  // Props types
  'ButtonProps': '@mui/material/Button',
  'TextFieldProps': '@mui/material/TextField',
  'SelectProps': '@mui/material/Select',
  'AppBarProps': '@mui/material/AppBar',
};

function updateFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Find all import statements from @mui/material
    const importRegex = /import\s+(?:{([^}]+)}|\*\s+as\s+\w+|\w+(?:\s*,\s*{[^}]+})?)\s+from\s+['"]@mui\/material['"]/g;
    
    let match;
    const imports = [];
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push({
        fullMatch: match[0],
        components: match[1] ? match[1].split(',').map(c => c.trim()) : []
      });
    }
    
    if (imports.length === 0) return false;
    
    // Process each import
    for (const importStatement of imports) {
      if (importStatement.components.length > 0) {
        const newImports = [];
        
        for (const component of importStatement.components) {
          const cleanComponent = component.trim();
          if (componentMap[cleanComponent]) {
            newImports.push(`import ${cleanComponent} from '${componentMap[cleanComponent]}'`);
          } else {
            // Fallback for unknown components
            newImports.push(`import ${cleanComponent} from '@mui/material/${cleanComponent}'`);
          }
        }
        
        content = content.replace(importStatement.fullMatch, newImports.join('\n'));
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Updated: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Get list of files to process
const filesToProcess = [
  // Add the remaining files here
];

console.log('Starting MUI import updates...');
let updatedCount = 0;

for (const file of filesToProcess) {
  if (updateFile(file)) {
    updatedCount++;
  }
}

console.log(`\nCompleted! Updated ${updatedCount} files.`);
