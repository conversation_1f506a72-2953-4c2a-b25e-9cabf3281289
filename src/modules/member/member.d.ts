import { Dispatch, SetStateAction } from 'react'
import { MemberChannels, MemberStatus } from './constants/member.constant'

declare global {
  interface IMemberTag {
    name: string
  }

  interface IMemberFinancialActivity {
    id: number
    member_id: number
    type: string
    amount: number
    currency: string
    date: string
  }

  interface IMemberActivity {
    type: string
    value: string
    member_id: number
  }

  interface IMember {
    id: number
    name: string | null
    ref_id: string
    player_id: string
    country_code: CountryCode
    phone_number: string
    email: string | null
    registration_date: string
    telemarketing_id: number | null
    master_contact_id: number | null
    upline: string
    upline_has_checked: boolean
    ref_code: string | null
    status: MemberStatus
    total_deposit: number | null
    total_withdraw: number | null
    deposit_adjustment: number | null
    withdraw_adjustment: number | null
    total_adjustment: number | null
    last_deposit_amount: number | null
    last_deposit_at: string | null // ISO 8601 string for timestamps
    balance: number | null
    registered_from: string | null
    notes: string | null
    avatar_text_color: string | null
    bank_name: string | null
    bank_account: string | null
    bank_account_name: string | null
    bank_account_number: string | null
    member_group: string | null
    created_at: string // ISO 8601 string
    updated_at: string // ISO 8601 string
    deleted_at: string | null // ISO 8601 string for soft deletes
    claim_regis_at: string
    claim_regis_by: string
    claim_fd_at: string
    claim_fd_by: IUser['id']
    channel: MemberChannels
    imported_by: IUser['id']
    tags: IMemberTag[]
    financialActivities: IMemberFinancialActivity[]
    activities: IMemberActivity[]
    telemarketing: IUser | null
  }
}

export {}
