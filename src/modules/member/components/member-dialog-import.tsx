import { useState, Fragment, useCallback, FC, memo, useEffect } from 'react'
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import Button from '@mui/material/Button'
import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import Paper from '@mui/material/Paper'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import ExportIcon from '@/assets/icons/lets-icons--export.svg?react'
import { MemberApi } from '@/modules/member/api/member.api'
import { Select } from '@/modules/app/components/core'
import { ChannelOptions, MemberChannels } from '../constants/member.constant'
import { Submit<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import AbsoluteFillLoading from '@/modules/app/components/absolute-fill-loading'
import DraggableFileUploader from '@/modules/app/components/draggable-file-uploader'
import toast from 'react-hot-toast'
import { Swal2 } from '@/plugins/sweetalert2'

// const maxSizeInMB = 100
// const acceptedFormats = ['.csv']

interface Props {
  onUploadSuccess: () => void
}

interface FormValues {
  channel: string | null
  file: File | null
}

const schema = yup
  .object({
    channel: yup.string().required('Name is required'),
  })
  .required()

const MemberDialogImport: FC<Props> = ({ onUploadSuccess }) => {
  const { handleSubmit, control, reset, watch, setValue } = useForm<FormValues>({
    // @ts-ignore
    resolver: yupResolver(schema),
    defaultValues: {
      channel: MemberChannels.LUX,
      file: null,
    },
  })
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [csvData, setCsvData] = useState<string[][]>([])
  const [isLoading, setIsLoading] = useState(false)
  // const [uploadInProgress, setUploadInProgress] = useState(false)

  const handleOpenDialog = () => {
    setIsDialogOpen(true)
    reset()
  }

  const onClose = useCallback(() => {
    setIsDialogOpen(false)
    setCsvData([])
    // setUploadInProgress(false)
  }, [isDialogOpen, csvData])

  useEffect(() => {
    if (isDialogOpen) {
      setTimeout(() => {
        setValue('channel', MemberChannels.LUX)
      }, 500)
    }
  }, [isDialogOpen])

  const onValidSubmit: SubmitHandler<FormValues> = values => {
    setIsLoading(true)
    toast
      .promise(
        MemberApi.import({ file: values.file, channel: values.channel }),
        {
          loading: 'Uploading file... Please do not close the app',
          success: <b>Member saved!</b>,
          error: <b>Could not import member.</b>,
        },
        { position: 'bottom-center' }
      )
      .then(response => {
        console.log('toast.promise', response)
        onClose()
        onUploadSuccess()
        Swal2.fire({
          title: 'Data member berhasil di upload.',
          text: 'Proses sinkronasi dilakukan dibalik layar, mohon menunggu hingga proses selesai.',
          icon: 'success',
        })
      })
      .finally(() => {
        onClose()
        onUploadSuccess()
        setIsLoading(false)
      })
  }

  const onInvalidSubmit: SubmitErrorHandler<FormValues> = useCallback(errors => {
    console.log('errors', errors)
  }, [])

  const handleClose = () => {
    reset()
    onClose()
  }

  const onClickBackdrop = useCallback(() => {
    if (isDialogOpen && isLoading) {
      toast.error('Please wait for the process to complete.')
    } else {
      setIsDialogOpen(false)
    }
  }, [isDialogOpen, isLoading])

  const handleExternalSubmitClick = () => {
    // @ts-ignore
    handleSubmit(onValidSubmit, onInvalidSubmit)() // Notice the extra () at the end
  }

  const onSelectCsvFile = useCallback((file: File) => {
    setValue('file', file)
  }, [])

  return (
    <Fragment>
      <Button
        onClick={handleOpenDialog}
        size={'small'}
        variant='text'
        startIcon={<Box component={ExportIcon} sx={{ height: 22, width: 22, transform: 'rotate(180deg)' }} />}
      >
        Import
      </Button>
      <Dialog
        open={isDialogOpen}
        onClose={onClickBackdrop}
        aria-labelledby='form-dialog-import-db'
        maxWidth={csvData?.length > 0 ? 'md' : 'sm'}
        fullWidth
        sx={{
          '& .MuiPaper-root': { borderRadius: 4 },
          '& .MuiDialog-paper': {
            boxShadow: 0,
          },
          transition: theme => theme.transitions.create(['width']),
        }}
      >
        <DialogTitle>Import Member (CSV)</DialogTitle>
        <DialogContent sx={{ px: 0, py: 0 }}>
          <Box
            component='form'
            // @ts-ignore
            onSubmit={handleSubmit(onValidSubmit, onInvalidSubmit)}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              py: 2,
              gap: 1,
            }}
          >
            <Stack
              sx={{
                width: {
                  xs: '100%',
                  md: 320,
                },
              }}
            >
              <Select
                name='channel'
                size='small'
                label='Channel'
                // @ts-ignore
                control={control}
                options={ChannelOptions}
                rules={{ required: 'This field is required' }}
              />
            </Stack>
            <DraggableFileUploader
              isLoading={isLoading}
              selectedFile={watch('file') as File}
              onSelectFile={onSelectCsvFile}
              onRemoveFile={() => setValue('file', null)}
            />
            {/* CSV Data Table */}
            {csvData.length > 0 && (
              <TableContainer
                component={Paper}
                sx={{
                  border: theme => `1px solid ${theme.palette.divider}`,
                  width: '100%',
                  height: 260,
                  overflow: 'scroll',
                  borderRadius: '5px !important',
                }}
              >
                <Table stickyHeader size='small' aria-label='csv data table'>
                  <TableHead>
                    <TableRow className='bg-blue-100'>
                      {/* Render table headers from the first row of CSV data */}
                      {csvData[0].map((header, index) => (
                        <TableCell
                          key={index}
                          className='font-bold text-blue-800 text-lg py-3'
                          sx={{ backgroundColor: 'primary.main', color: 'primary.contrastText', fontWeight: 500 }}
                        >
                          {header ?? '-'}
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {/* Render table rows starting from the second row (data rows) */}
                    {csvData.slice(1).map((row, rowIndex) => (
                      <TableRow key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                        {row.map((cell, cellIndex) => (
                          <TableCell key={cellIndex} className='text-gray-800 py-2'>
                            {cell ?? '-'}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color='error' size='medium' disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleExternalSubmitClick} color='primary' size='medium' disabled={!watch('file') || isLoading}>
            Upload
          </Button>
        </DialogActions>
        {isLoading && <AbsoluteFillLoading />}
      </Dialog>
    </Fragment>
  )
}

export default memo(MemberDialogImport)
