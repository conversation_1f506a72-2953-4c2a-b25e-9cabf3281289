import { useCallback, useState, memo, useEffect } from 'react'

import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import Button from '@mui/material/Button'
import IconButton from '@mui/material/IconButton'
import TextField from '@mui/material/TextField'
import ButtonGroup from '@mui/material/ButtonGroup'
import Divider from '@mui/material/Divider'

// icons
import ListIcon from '@/assets/icons/mi--list.svg?react'
import SyncIcon from '@/assets/icons/bx--sync.svg?react'
import TableIcon from '@/assets/icons/ci--table.svg?react'
import GridIcon from '@/assets/icons/si--grid-fill.svg?react'
import ExportIcon from '@/assets/icons/lets-icons--export.svg?react'
import SearchIcon from '@/assets/icons/weui--search-filled.svg?react'

// hooks
import { useMember } from '@/modules/member/hooks'
import { useAppDispatch } from '@/plugins/redux'

// components
import { IMemberQueryParams } from '../api/member.api'
import useDebounce from '@/modules/app/hooks/useDebounce.hook'
import MemberDialogImport from './member-dialog-import'

// components
const MemberToolbar = () => {
  const dispatch = useAppDispatch()

  const { member_fetchList, member_queryParams } = useMember()

  const fetchMembers = (params?: Partial<IMemberQueryParams>) => {
    dispatch(member_fetchList({ ...member_queryParams, ...params }))
  }

  const [searchExpanded, setSearchExpanded] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // Function to toggle the expanded state of the search bar
  const handleToggleExpand = () => {
    setSearchExpanded(!searchExpanded)
    // Clear search term when collapsing the search bar
    if (searchExpanded) {
      setSearchTerm('')
    }
  }

  // Function to handle search input changes
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
  }

  // Function to handle search submission (e.g., when pressing Enter or clicking search icon)
  const handleSearchSubmit = () => {
    fetchMembers({ query: searchTerm, page: 1, perPage: member_queryParams.perPage })
  }

  const handleActionSuccess = useCallback(() => {
    dispatch(member_fetchList(member_queryParams))
  }, [member_queryParams])

  // Get the debounced value after 500ms
  const debouncedSearchTerm = useDebounce(searchTerm, 500)

  // Effect to "perform the search" when the debounced value changes
  useEffect(() => {
    if (debouncedSearchTerm) {
      // This is where you would make an API call with the debouncedSearchTerm
      fetchMembers({ query: debouncedSearchTerm, page: 1, perPage: member_queryParams.perPage })
      // performApiCall(debouncedSearchTerm);
    }
  }, [debouncedSearchTerm])

  return (
    <Box sx={{}}>
      <Stack direction='row' justifyContent='space-between' alignItems='center'>
        <Stack direction='row' gap={2}>
          <Stack sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
            <ButtonGroup aria-label='filter view group btn'>
              <Button variant='text' startIcon={<Box component={TableIcon} sx={{ height: 16, width: 16 }} />}>
                Table View
              </Button>
              <Button variant='text' startIcon={<Box component={GridIcon} sx={{ height: 14, width: 14 }} />}>
                Grid View
              </Button>
              <Button variant='text' startIcon={<Box component={ListIcon} sx={{ height: 20, width: 20 }} />}>
                List View
              </Button>
            </ButtonGroup>
          </Stack>
          <Stack direction='row' gap={1} alignItems='center'>
            {/* search */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              {/* Button to toggle the search bar expansion */}
              <IconButton size='small' edge='start' onClick={handleToggleExpand} sx={{ mr: 2 }}>
                <Box component={SearchIcon} sx={{ height: 22, width: 22 }} />
              </IconButton>

              {/* Search Input Field */}
              <TextField
                size='small'
                variant='outlined'
                placeholder='Search...'
                value={searchTerm}
                onChange={handleSearchChange}
                onKeyPress={e => {
                  if (e.key === 'Enter') {
                    handleSearchSubmit()
                  }
                }}
                sx={{
                  width: searchExpanded ? '200px' : '0px',
                  opacity: searchExpanded ? 1 : 0,
                  transition: 'width 0.3s ease-in-out, opacity 0.3s ease-in-out', // Smooth transition
                  overflow: 'hidden', // Hide overflow content during transition
                  borderRadius: '8px', // Rounded corners for text field
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '8px',
                    paddingRight: searchExpanded ? '0px' : '0px', // Adjust padding when collapsed
                  },
                  '& .MuiInputBase-input': {},
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#e0e0e0', // Light border color
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#bdbdbd', // Darker border on hover
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#1976d2', // Material-UI primary color on focus
                  },
                }}
              />
            </Box>
            <IconButton size='small' edge='start' onClick={() => fetchMembers()} sx={{ mr: 2 }}>
              <Box component={SyncIcon} sx={{ height: 22, width: 22 }} />
            </IconButton>
          </Stack>
        </Stack>
        <Stack direction='row' gap={2}>
          <MemberDialogImport onUploadSuccess={handleActionSuccess} />
          <Button size='small' variant='text' startIcon={<Box component={ExportIcon} sx={{ height: 22, width: 22 }} />}>
            Export
          </Button>
        </Stack>
      </Stack>
      <Divider sx={{ my: 2 }} />
    </Box>
  )
}

export default memo(MemberToolbar)
