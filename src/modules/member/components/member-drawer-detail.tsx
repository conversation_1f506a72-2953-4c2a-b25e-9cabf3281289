import { FC, Fragment, memo, useCallback } from 'react'
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  Avatar,
  Chip,
  Divider,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import EmailIcon from '@mui/icons-material/Email'
import PhoneIcon from '@mui/icons-material/Phone'
import CalendarTodayIcon from '@mui/icons-material/CalendarToday'
import { AppUtils } from '@/modules/app/utils/app.util'
import { MemberUtils } from '../utils/member.util'

interface Props {
  open: boolean
  onClose: () => void
  member?: IMember | null
}

const MemberDrawerDetail: FC<Props> = ({ open, onClose, member }) => {
  const onCloseDrawer = useCallback(() => {
    onClose?.()
  }, [onClose])

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <div>
      <Drawer
        anchor='right'
        open={open}
        onClose={onCloseDrawer}
        sx={{
          zIndex: 1300,
          mr: 4,
          '& .MuiPaper-root': {
            height: '100vh',
            backgroundColor: 'transparent !important',
            boxShadow: 0,
            backgroundImage: 'none',
            overflow: 'hidden',
          },
        }}
      >
        <Box
          sx={{
            my: 2,
            mr: 2,
            boxShadow: 0,
            borderRadius: 4,
            backgroundColor: 'background.paper',
            height: '100%',
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              width: 392,
              px: 4,
              pt: 2,
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              overflowY: 'scroll',
            }}
            role='presentation'
          >
            {/* Header */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 2 }}>
              <Typography variant='h6' component='h2'>
                Member Detail
              </Typography>
              <IconButton onClick={onCloseDrawer}>
                <CloseIcon />
              </IconButton>
            </Box>

            {member && (
              <Fragment>
                {/* Member Profile */}
                <Box sx={{ textAlign: 'center', mb: 3 }}>
                  <Avatar
                    sx={{
                      width: 80,
                      height: 80,
                      mx: 'auto',
                      mb: 2,
                      fontSize: '2rem',
                      bgcolor: member.avatar_text_color,
                    }}
                  >
                    {AppUtils.getFirstLetters(member.player_id)}
                  </Avatar>
                  <Typography variant='h5' gutterBottom>
                    {member.player_id}
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 2 }}>
                    <Chip
                      label={MemberUtils.getStatusName(member.status)}
                      // color={AppUtils.getStatusColor(member.status) as any}
                      size='small'
                    />
                    {/* <Chip label={member.membershipType} variant='outlined' size='small' /> */}
                  </Box>
                  {member.tags && member.tags.length > 0 && (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
                      {member.tags.map((tag, index) => (
                        <Chip key={index} label={tag.name} size='small' variant='outlined' />
                      ))}
                    </Box>
                  )}
                </Box>

                <Divider sx={{ mb: 3 }} />

                {/* Contact Information */}
                <Typography variant='h6' gutterBottom>
                  Contact Information
                </Typography>
                <List dense sx={{ mb: 2 }}>
                  {member.email && (
                    <ListItem disablePadding>
                      <ListItemIcon>
                        <EmailIcon color='primary' />
                      </ListItemIcon>
                      <ListItemText primary={member.email} secondary='Email' />
                    </ListItem>
                  )}
                  {member.phone_number && (
                    <ListItem disablePadding>
                      <ListItemIcon>
                        <PhoneIcon color='primary' />
                      </ListItemIcon>
                      <ListItemText primary={member.country_code + member.phone_number} secondary='No HP' />
                    </ListItem>
                  )}
                  {/* {member.website && (
                <ListItem disablePadding>
                  <ListItemIcon>
                    <LanguageIcon color='primary' />
                  </ListItemIcon>
                  <ListItemText primary={member.website} secondary='Website' />
                </ListItem>
              )} */}
                </List>

                <Divider sx={{ mb: 3 }} />

                {/* Personal Information */}
                <Typography variant='h6' gutterBottom>
                  Player Information
                </Typography>
                <List dense sx={{ mb: 2 }}>
                  {member.ref_code && (
                    <ListItem disablePadding>
                      <ListItemIcon>
                        <PhoneIcon color='primary' />
                      </ListItemIcon>
                      <ListItemText primary={member.ref_code} secondary='Phone' />
                    </ListItem>
                  )}
                  {member.registered_from && (
                    <ListItem disablePadding>
                      <ListItemIcon>
                        <PhoneIcon color='primary' />
                      </ListItemIcon>
                      <ListItemText primary={member.registered_from} secondary='Phone' />
                    </ListItem>
                  )}
                  {member.registration_date && (
                    <ListItem disablePadding>
                      <ListItemIcon>
                        <PhoneIcon color='primary' />
                      </ListItemIcon>
                      <ListItemText primary={member.registration_date} secondary='Phone' />
                    </ListItem>
                  )}
                  {/* {member.website && (
                <ListItem disablePadding>
                  <ListItemIcon>
                    <LanguageIcon color='primary' />
                  </ListItemIcon>
                  <ListItemText primary={member.websiNo HP:te} secondary='Website' />
                </ListItem>
              )} */}
                </List>
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid size={{ xs: 12 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <CalendarTodayIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                      <Typography variant='caption' color='text.secondary'>
                        Registered at
                      </Typography>
                    </Box>
                    <Typography variant='body2'>{formatDate(member.registration_date as string)}</Typography>
                  </Grid>
                  {/* {member.dateOfBirth && (
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <CakeIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                    <Typography variant='caption' color='text.secondary'>
                      Date of Birth
                    </Typography>
                  </Box>
                  <Typography variant='body2'>{formatDate(member.dateOfBirth)}</Typography>
                </Grid>
              )} */}
                  {/* {member.gender && (
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <PersonIcon sx={{ mr: 1, color: 'text.secondary', fontSize: 16 }} />
                    <Typography variant='caption' color='text.secondary'>
                      Gender
                    </Typography>
                  </Box>
                  <Typography variant='body2'>{member.gender.charAt(0).toUpperCase() + member.gender.slice(1)}</Typography>
                </Grid>
              )} */}
                </Grid>

                <Divider sx={{ mb: 3 }} />
                <Typography variant='h6' gutterBottom>
                  Statistics
                </Typography>
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid size={{ xs: 12 }}>
                    <Card variant='outlined' sx={{ height: 'unset !important' }}>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant='h6' color='primary'>
                          {member.total_deposit || 0}
                        </Typography>
                        <Typography variant='caption' color='text.secondary'>
                          Total Deposit
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <Card variant='outlined' sx={{ height: 'unset !important' }}>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant='h6' color='primary'>
                          {member.total_withdraw || 0}
                        </Typography>
                        <Typography variant='caption' color='text.secondary'>
                          Total Withdraw
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <Card variant='outlined' sx={{ height: 'unset !important' }}>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant='h6' color='primary'>
                          {member.deposit_adjustment || 0}
                        </Typography>
                        <Typography variant='caption' color='text.secondary'>
                          Deposit Adjustment
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <Card variant='outlined' sx={{ height: 'unset !important' }}>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant='h6' color='primary'>
                          {member.withdraw_adjustment || 0}
                        </Typography>
                        <Typography variant='caption' color='text.secondary'>
                          Withdraw Adjustment
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <Card variant='outlined' sx={{ height: 'unset !important' }}>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant='h6' color='primary'>
                          {member.total_adjustment || 0}
                        </Typography>
                        <Typography variant='caption' color='text.secondary'>
                          Total Adjustment
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <Card variant='outlined' sx={{ height: 'unset !important' }}>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant='h6' color='primary'>
                          {member.last_deposit_amount || 0}
                        </Typography>
                        <Typography variant='caption' color='text.secondary'>
                          Last Deposit Amount
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid size={{ xs: 12 }}>
                    <Card variant='outlined' sx={{ height: 'unset !important' }}>
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant='h6' color='primary'>
                          {member.last_deposit_at || 0}
                        </Typography>
                        <Typography variant='caption' color='text.secondary'>
                          Last Deposit Date
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                {member.activities?.length > 0 && (
                  <Fragment>
                    {member.activities?.map((activity, index) => (
                      <Fragment key={index}>
                        <Divider sx={{ mb: 3 }} />
                        <Typography variant='h6' gutterBottom>
                          Activity {activity.type + 1}
                        </Typography>
                      </Fragment>
                    ))}
                  </Fragment>
                )}

                {/* Notes */}
                {member.notes && (
                  <>
                    <Divider sx={{ mb: 3 }} />
                    <Typography variant='h6' gutterBottom>
                      Notes
                    </Typography>
                    <Card variant='outlined' sx={{ mb: 2 }}>
                      <CardContent>
                        <Typography variant='body2'>{member.notes}</Typography>
                      </CardContent>
                    </Card>
                  </>
                )}
              </Fragment>
            )}
          </Box>
        </Box>
      </Drawer>
    </div>
  )
}

export default memo(MemberDrawerDetail)
