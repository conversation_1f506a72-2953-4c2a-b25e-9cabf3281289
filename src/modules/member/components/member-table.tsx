import { FC, memo, useCallback, useState } from 'react'
import Box from '@mui/material/Box'
import Paper from '@mui/material/Paper'
import Typography from '@mui/material/Typography'
import { DataGrid, GridColDef } from '@mui/x-data-grid'
import ArrowUpIcon from '@/assets/icons/mdi--arrow-up.svg?react'
import BookmarkIcon from '@/assets/icons/solar--folder-with-files-bold-duotone.svg?react'

// toast
import toast from 'react-hot-toast'

// assets
import { Avatar, Button, ButtonGroup, IconButton, Stack, TablePagination } from '@mui/material'
import { ROWS_PER_PAGE_OPTIONS } from '@/modules/app/constants'
import DialogConfirm from '@/modules/app/components/dialog-confirm'

// utils
import { AppUtils } from '@/modules/app/utils/app.util'
import { UserUtils } from '@/modules/user/utils/user.util'

// apis
import { IMemberQueryParams, MemberApi } from '../api/member.api'
import MemberDrawerDetail from './member-drawer-detail'

interface Props {
  rows: IMember[]
  paginate: IPaginateMeta
  fetchData: (params: Partial<IMemberQueryParams>) => void
}

const MemberTable: FC<Props> = ({ rows, paginate, fetchData }) => {
  const [selectedMember, setSelectedMember] = useState<IMember | null>(null)
  const [deleteId, setDeleteId] = useState<number | null>(null)

  const handleChangePage = (_: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    fetchData({ page: newPage + 1 })
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    fetchData({ page: 1, perPage: parseInt(event.target.value, 10) })
  }

  const handleClick = () => {}

  const onConfirmDelete = useCallback(async (itemId?: number) => {
    try {
      const response = await MemberApi.delete(itemId as number)
      if (response) {
        handleCloseDialogConfirmDelete()
        toast.success('User deleted.', { duration: 3000, position: 'bottom-center' })
        fetchData({ page: paginate.current_page })
      }
    } catch (e) {
      console.log('e', e)
      toast.error('Failed to delete user.', { duration: 3000, position: 'bottom-center' })
    }
  }, [])

  const handleCloseDialogConfirmDelete = useCallback(() => {
    setDeleteId(null)
  }, [deleteId])

  const onCloseDrawerDetail = useCallback(() => {
    setSelectedMember(null)
  }, [selectedMember])

  const onClickMemberDetail = useCallback(
    (member: IMember) => {
      setSelectedMember(member)
    },
    [selectedMember]
  )

  const columns: GridColDef<(typeof rows)[number]>[] = [
    {
      field: 'id',
      headerName: 'ID',
      sortable: true,
      width: 80,
      renderCell: params => (
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            cursor: 'pointer',
            '&:hover': {
              color: 'primary.main',
              textDecoration: 'underline',
            },
          }}
        >
          <Stack onClick={() => onClickMemberDetail(params.row)}>
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-start',
                mb: 0.4,
              }}
            >
              <Typography sx={{ fontWeight: 700, fontSize: '0.8rem', lineHeight: 1, color: 'primary.main' }}>{params?.value}</Typography>
              <Box component={ArrowUpIcon} sx={{ height: 17, width: 17, ml: 0.2, transform: 'rotate(45deg)' }} />
            </Box>
            <Typography
              sx={{ fontWeight: 500, fontSize: '0.6rem', lineHeight: 1, color: 'text.secondary', textDecoration: 'none !important' }}
            >
              {params?.row.ref_id}
            </Typography>
          </Stack>
        </Box>
      ),
    },
    {
      field: 'channel',
      headerName: 'Channel',
      sortable: true,
      width: 100,
      renderCell: params => (
        <Box sx={{ height: '100%', flexDirection: 'row', display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box component={BookmarkIcon} sx={{ height: 16, width: 16, color: 'primary.main' }} />
          <Typography sx={{ fontSize: '0.8rem', lineHeight: 1, fontWeight: '600', textTransform: 'uppercase' }}>{params.value}</Typography>
        </Box>
      ),
    },
    {
      field: 'player_id',
      headerName: 'User ID',
      width: 160,
      sortable: true,
      editable: false,
      renderCell: params => (
        <Stack
          sx={{
            width: '100%',
            flexDirection: 'row',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-start',
            gap: 1,
            height: '100%',
          }}
          onClick={() => onClickMemberDetail(params.row)}
        >
          <IconButton color='inherit' aria-label='user profile' sx={{ p: 0 }} onClick={handleClick}>
            <Avatar
              alt='User Avatar'
              title={params.value}
              sx={{
                width: 22,
                height: 22,
                bgcolor: params.row.avatar_text_color,
                fontWeight: 500,
                fontSize: 22 * 0.55,
                lineHeight: 1,
              }}
            >
              {AppUtils.getFirstLetters(params.value)}
            </Avatar>
          </IconButton>
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'center' }}>
            <Typography sx={{ fontWeight: '400', fontSize: '0.9rem', lineHeight: 1.7 }}>{params.value}</Typography>
            <Typography sx={{ fontWeight: '400', fontSize: '0.75rem', color: 'text.secondary', lineHeight: 1.2 }}>
              {/* {params.row.username} */}
            </Typography>
          </Box>
        </Stack>
      ),
    },
    {
      field: 'phone_number',
      sortable: true,
      headerName: 'Phone Number',
      description: 'This column has a value getter and is not sortable.',
      width: 160,
      valueGetter: (_, row) => `+${row.country_code || ''}  ${row.phone_number || ''}`,
    },
    {
      field: 'email',
      headerName: 'Email',
      sortable: false,
      description: 'This column has a value getter and is not sortable.',
      width: 180,
      valueGetter: (_, row) => `${row?.email || '-'}`,
    },
    {
      field: 'upline',
      headerName: 'Upline',
      width: 120,
      editable: false,
      headerAlign: 'left',
      renderCell: params => {
        return (
          <Stack direction='row' spacing={1} alignItems='center' sx={{ height: '100%' }}>
            <Box>
              <Typography sx={{ fontWeight: '400', fontSize: '0.85rem' }}>{params?.value}</Typography>
            </Box>
          </Stack>
        )
      },
    },
    {
      field: 'telemarketing_id',
      headerName: 'Telemarketing',
      width: 140,
      editable: false,
      headerAlign: 'left',
      renderCell: params => {
        if (!params.row?.telemarketing) return '-'
        return (
          <Stack direction='row' spacing={1} alignItems='center' sx={{ height: '100%' }}>
            <Avatar
              alt='User Avatar'
              title={params.value}
              sx={{
                width: 22,
                height: 22,
                bgcolor: params.row?.telemarketing?.avatar_text_color ?? 'primary.main',
                fontSize: 22 * 0.5,
                lineHeight: 0,
                fontWeight: 500,
              }}
            >
              {AppUtils.getFirstLetters(params?.row?.telemarketing?.name)}
            </Avatar>
            <Box>
              <Typography sx={{ fontWeight: '400', fontSize: '0.85rem' }}>{params?.row?.telemarketing?.name}</Typography>
            </Box>
          </Stack>
        )
      },
    },
    {
      field: 'registration_date',
      headerName: 'Registration date',
      sortable: true,
      width: 150,
    },
    {
      field: 'ref_code',
      headerName: 'Ref code',
      sortable: true,
      width: 132,
    },
    {
      field: 'total_deposit',
      headerName: 'Total Deposit',
      sortable: true,
      width: 172,
    },
    {
      field: 'total_withdraw',
      headerName: 'Total Withdraw',
      sortable: true,
      width: 172,
    },
    {
      field: 'deposit_adjustment',
      headerName: 'Deposit Adjustment',
      sortable: true,
      width: 160,
    },
    {
      field: 'withdraw_adjustment',
      headerName: 'Withdraw Adjustment',
      sortable: true,
      width: 160,
    },
    {
      field: 'total_adjustment',
      headerName: 'Total Adjustment',
      sortable: true,
      width: 160,
    },
    {
      field: 'last_deposit_amount',
      headerName: 'Last Deposit Amount',
      sortable: true,
      width: 132,
    },
    {
      field: 'last_deposit_at',
      headerName: 'Last Deposit',
      sortable: true,
      width: 132,
    },
    {
      field: 'status',
      headerName: 'Status',
      sortable: true,
      width: 132,
      renderCell: params => (
        <Box sx={{ height: '100%', flexDirection: 'row', display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            component={UserUtils.getStatusIcon(params.value)}
            sx={{ height: 16, width: 16, color: UserUtils.getStatusColor(params.value) }}
          />
          <Typography sx={{ fontSize: '0.8rem', lineHeight: 1, color: UserUtils.getStatusColor(params.value), fontWeight: '400' }}>
            {UserUtils.getStatusName(params.value)}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'country_code',
      headerName: 'Actions',
      width: 200,
      sortable: false,
      editable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <ButtonGroup size='small' aria-label='filter view group btn'>
          <Button variant='outlined' size='small' color='success' onClick={() => onClickMemberDetail(params.row)}>
            View
          </Button>
        </ButtonGroup>
      ),
    },
  ]

  return (
    <Paper
      elevation={0}
      sx={{
        p: 0,
        width: '100%',
        '& .MuiDataGrid-columnHeadersContainer': {
          backgroundColor: 'background.paper',
        },
      }}
    >
      <DataGrid
        rowHeight={52}
        rows={rows}
        columns={columns}
        checkboxSelection
        // initialState={{
        //   pagination: {
        //     paginationModel: {
        //       pageSize: paginate.per_page,
        //       page: paginate.current_page - 1,
        //     },
        //   },
        // }}
        sx={{
          backgroundColor: 'background.paper',
          borderColor: 'divider',
          '& .MuiDataGrid-cell': {
            fontSize: '0.9rem',
            fontWeight: '400',
          },
          '& .MuiDataGrid-columnHeaderTitle': {
            fontWeight: 500,
            fontSize: '0.9rem',
          },
        }}
        hideFooter
        disableRowSelectionOnClick
      />
      <TablePagination
        component='div'
        count={paginate.total}
        page={paginate.current_page - 1}
        onPageChange={handleChangePage}
        rowsPerPage={paginate.per_page}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[...ROWS_PER_PAGE_OPTIONS]}
      />
      <DialogConfirm
        itemId={deleteId as number}
        open={Boolean(deleteId)}
        onCancel={handleCloseDialogConfirmDelete}
        onConfirm={onConfirmDelete}
        title='Are you sure?'
        subtitle="You won't be able to revert this!"
      />
      <MemberDrawerDetail open={Boolean(selectedMember)} onClose={onCloseDrawerDetail} member={selectedMember} />
    </Paper>
  )
}

export default memo(MemberTable)
