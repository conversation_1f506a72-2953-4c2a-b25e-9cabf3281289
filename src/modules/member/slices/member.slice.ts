// redux toolkit
import { createSlice } from '@reduxjs/toolkit'

import { member_fetchList } from './member.thunk'
import { IMemberQueryParams } from '../api/member.api'
import { ROWS_PER_PAGE_OPTIONS } from '@/modules/app/constants'

// type for our state
export type IMemberSlice = {
  member_listData: IMember[]
  member_listLoading: boolean
  member_paginate: IPaginateMeta
  member_queryParams: IMemberQueryParams
}

// initial state
export const member_initialState: IMemberSlice = {
  member_listData: [],
  member_listLoading: false,
  member_paginate: {
    current_page: 1,
    from: 0,
    per_page: 0,
    to: 0,
    total: 0,
  },
  member_queryParams: {
    query: null,
    paginate: 1,
    perPage: ROWS_PER_PAGE_OPTIONS[0],
    page: 1,
  },
}

export const memberSlice = createSlice({
  name: 'member',
  initialState: member_initialState,
  reducers: {
    member_reset: () => member_initialState,
  },
  extraReducers: builder => {
    builder.addCase(member_fetchList.pending, (state, _) => {
      state.member_listLoading = true
    })
    builder.addCase(member_fetchList.rejected, (state, _) => {
      state.member_listLoading = false
    })

    builder.addCase(member_fetchList.fulfilled, (state, action) => {
      state.member_listLoading = false

      if (Array.isArray(action.payload.data)) {
        state.member_listData = action.payload.data
      }

      if (action.payload.meta) {
        state.member_paginate = action.payload.meta

        state.member_queryParams.paginate = 1
        state.member_queryParams.page = action.payload.meta.current_page
        state.member_queryParams.perPage = action.payload.meta.per_page
      }

      // get query
      if (action.meta?.arg?.query) {
        state.member_queryParams.query = action.meta?.arg?.query
        // state.member_queryParams.query = action.meta.arg.query
      }
    })
  },
})

export const member_reducerActions = memberSlice.actions

export const member_selector = (state: RootState): IMemberSlice => state.member
