// constants
import { UserRoles } from '@/modules/user/constants/user.constant'

import SupportIcon from '@/assets/icons/ic--baseline-support-agent.svg?react'
import CloseOutlinedIcon from '@/assets/icons/ri--close-circle-fill.svg?react'
import CheckedOutlineIcon from '@/assets/icons/lets-icons--check-fill.svg?react'
import SuperAdminIcon from '@/assets/icons/material-symbols--admin-panel-settings-rounded.svg?react'
import AdminIcon from '@/assets/icons/eos-icons--admin.svg?react'
import { MemberStatus } from '../constants/member.constant'

const getStatusColor = (status: MemberStatus) => {
  switch (status) {
    case MemberStatus.INACTIVE:
      return '#fa4622'
    case MemberStatus.ACTIVE:
      return '#009f5f'
    default:
      return '#626262'
  }
}

const getStatusIcon = (status: MemberStatus) => {
  switch (status) {
    case MemberStatus.INACTIVE:
      return CloseOutlinedIcon
    case MemberStatus.ACTIVE:
      return CheckedOutlineIcon
    default:
      return CheckedOutlineIcon
  }
}

const getStatusName = (status: MemberStatus) => {
  switch (status) {
    case MemberStatus.INACTIVE:
      return 'Inactive'
    case MemberStatus.ACTIVE:
      return 'Active'
    default:
      return 'Unknown'
  }
}

const getRoleColor = (status: UserRoles) => {
  switch (status) {
    case UserRoles.SUPER_ADMIN:
      return '#f7176d'
    case UserRoles.ADMIN:
      return '#1685ed'
    case UserRoles.TELEMARKETING:
      return '#e48005'
    default:
      return '#626262'
  }
}

const getRoleIcon = (status: UserRoles) => {
  switch (status) {
    case UserRoles.SUPER_ADMIN:
      return SuperAdminIcon
    case UserRoles.ADMIN:
      return AdminIcon
    case UserRoles.TELEMARKETING:
      return SupportIcon
    default:
      return SupportIcon
  }
}

const getRoleName = (status: UserRoles) => {
  switch (status) {
    case UserRoles.SUPER_ADMIN:
      return 'Super Admin'
    case UserRoles.ADMIN:
      return 'Admin'
    case UserRoles.TELEMARKETING:
      return 'Telemarketing'
    default:
      return 'Unknown'
  }
}

export const MemberUtils = {
  getStatusColor,
  getStatusIcon,
  getStatusName,
  getRoleColor,
  getRoleIcon,
  getRoleName,
}
