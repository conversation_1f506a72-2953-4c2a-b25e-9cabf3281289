import axiosInstance from '@/plugins/axios/axios-instance'

export interface IMemberQueryParams extends IBasePaginateRequest {
  username?: string | null
  email?: string | null
  phone_number?: string | null
  join_date?: string | null
  query?: string | null
}

export interface IStoreMemberRequest {
  name: string
  email: string
  username: string
  password: string
  phone_number: string | null
  join_date: string | null
  notes?: string | null
  status: IMember['status'] | null
}

export interface IMemberImportRequest {
  file: File | null
  channel: string | null
}

export const MemberApi = {
  fetchList: async (_params: IMemberQueryParams): Promise<IPaginateResponse<IMember>> => {
    const params: IMemberQueryParams = {
      // @ts-ignore
      paginate: 1,
      ..._params,
    }
    const response = await axiosInstance.get('/member', { params })
    return response?.data
  },

  create: async (body: IStoreMemberRequest): Promise<IMember> => {
    const response = await axiosInstance.post('/member', body)
    return response?.data
  },

  delete: async (memberId: number): Promise<IBaseApiResponse<unknown>> => {
    const response = await axiosInstance.delete(`/member/${memberId}`)
    return response.data
  },

  import: async (body: IMemberImportRequest): Promise<IBaseApiResponse<unknown>> => {
    const formData = new FormData()
    formData.append('file', body.file as File)
    formData.append('channel', String(body.channel))
    const response = await axiosInstance.post('/member/import', formData)
    return response?.data
  },
}
