import Box from '@mui/material/Box'
import { useCallback, useEffect } from 'react'
import { useAppDispatch } from '@/plugins/redux'

import CircularProgress from '@mui/material/CircularProgress'

// icons
import MemberTable from '@/modules/member/components/member-table'
import MemberToolbar from '@/modules/member/components/member-toolbar'
import PageHeader from '@/modules/app/components/page-header'
import GamepadIcon from '@/assets/icons/solar--gamepad-charge-bold-duotone.svg?react'

// apis
import { IMemberQueryParams } from '@/modules/member/api/member.api'

// hooks
import { useMember } from '@/modules/member/hooks'

const MemberScreen = () => {
  const dispatch = useAppDispatch()

  const { member_fetchList, member_listData, member_listLoading, member_paginate, member_queryParams } = useMember()

  const fetchData = useCallback(
    (params: Partial<IMemberQueryParams>) => {
      dispatch(member_fetchList({ ...member_queryParams, ...params }))
    },
    [member_queryParams]
  )

  useEffect(() => {
    fetchData(member_queryParams)
  }, [])

  return (
    <Box>
      {/* header  */}
      <PageHeader title='Member Management' subtitle='Manage member' icon={GamepadIcon} />
      <Box sx={{ px: 3, pt: 5 }}>
        <Box sx={{ backgroundColor: 'background.paper', px: 2, pb: 0.3, pt: 1.5, borderRadius: 2 }}>
          <MemberToolbar />
          {member_listLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
              <CircularProgress />
            </Box>
          ) : (
            // @ts-ignore
            <MemberTable rows={member_listData} paginate={member_paginate} fetchData={fetchData} />
          )}
        </Box>
      </Box>
    </Box>
  )
}

export default MemberScreen
