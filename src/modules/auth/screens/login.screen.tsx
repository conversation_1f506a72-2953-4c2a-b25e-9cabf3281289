import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import { FC, ReactElement, useState } from 'react'

// icons
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import AuthLayout from '../components/auth-layout'

// assets
import ForgotPasswordIconImg from '@/assets/icons/forgot-password.png'
import AppLogo from '@/modules/app/components/app-logo'
import BaseButton from '@/modules/app/components/core/base-button'

// configs
import { appConfig } from '@/modules/app/configs'

// components
import LoginForm from '../components/login-form'
import ClearCacheButton from '@/modules/app/components/clear-cache-btn'

const LoginScreen: FC = () => {
  const [forgottenPassword, setForgottenPassword] = useState<boolean>(false)

  const renderRequestNewPassword = (): ReactElement => {
    return (
      <Box sx={{ pb: 5 }}>
        <Box sx={{ mb: 3 }}>
          <BaseButton onClick={() => setForgottenPassword(false)} sx={{ ml: -2 }} variant='text' startIcon={<ArrowBackIcon />}>
            Kembali
          </BaseButton>
        </Box>

        <Box>
          <Box component='img' src={ForgotPasswordIconImg} sx={{ mb: 2, width: 80, height: 'auto' }} />
          <Typography sx={{ mb: 2 }} component='h1' variant='h2'>
            Lupa Password ?
          </Typography>
          <Typography sx={{ fontSize: 15 }}>
            Silahkan request password baru ke email{' '}
            <Typography component='span' sx={{ fontWeight: 'bold', color: 'primary.main', fontSize: 15 }}>
              <EMAIL>
            </Typography>
          </Typography>
        </Box>
      </Box>
    )
  }

  return (
    <AuthLayout>
      <Box
        sx={{
          display: 'flex',
          position: 'relative',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',

          width: {
            xs: '90%',
            sm: 460,
          },
          margin: '0 auto',
        }}
      >
        <Box
          sx={{
            width: '100%',
            px: { xs: 6, sm: 10 },
            py: { xs: 4, sm: 6 },
            borderRadius: 4,
            backgroundColor: theme => (theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.8)' : 'rgb(255 255 255)'),
            backdropFilter: 'blur(3px)',
          }}
        >
          {forgottenPassword ? (
            renderRequestNewPassword()
          ) : (
            <>
              <Box
                sx={{
                  width: '100%',
                  textAlign: 'center',
                  mb: 2,
                }}
              >
                <Box sx={{ mb: 3 }}>
                  <AppLogo sx={{ height: 50, width: 'auto' }} />
                </Box>
                <Typography gutterBottom component='h4' variant='h5'>
                  Sign In
                </Typography>
              </Box>

              {/* Login form */}
              <LoginForm />

              <Box sx={{ textAlign: 'center', mt: 3 }}>
                <Typography
                  sx={{
                    cursor: 'pointer',
                    color: 'primary.main',
                    fontWeight: 'bold',
                    mb: 3,
                  }}
                  onClick={() => setForgottenPassword(true)}
                >
                  Lupa Password ?
                </Typography>
              </Box>

              {/* Logo */}

              <Box textAlign='center'>
                <Typography
                  sx={{
                    fontWeight: 'bold',
                    color: 'text.primary',
                  }}
                >
                  {appConfig.appTitle}
                </Typography>
                <Box
                  sx={{
                    mt: 1,
                    px: 1.6,
                    py: 0.4,
                    fontSize: '0.74rem',
                    borderRadius: 5,
                    textAlign: 'center',
                    display: 'inline-block',
                    backgroundColor: '#04a572',
                    color: 'primary.contrastText',
                  }}
                >
                  v{appConfig.version}
                </Box>
              </Box>
            </>
          )}
        </Box>
      </Box>
      <Box sx={{ position: 'fixed', bottom: 12, right: 12 }}>
        <ClearCacheButton />
      </Box>
    </AuthLayout>
  )
}

export default LoginScreen
