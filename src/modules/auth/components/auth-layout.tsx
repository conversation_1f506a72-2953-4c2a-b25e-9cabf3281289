import { FC, ReactNode } from 'react'
import Box from '@mui/material/Box'
import { styled } from '@mui/material/styles'
import BackgroundImage from '@/assets/images/blobs-l-b49caff1a7941d98b60902da64956ad85dfbe4f9.webp'

export const WallPaper = styled('div')({
  zIndex: -1,
  position: 'absolute',
  width: '100%',
  height: '100%',
  top: 0,
  left: 0,
  overflow: 'hidden',
  backgroundImage: `url("${BackgroundImage}")`,
  backgroundPosition: 'center',
  backgroundSize: 'cover',
  '&:before': {
    content: '""',
    width: '140%',
    height: '140%',
    position: 'absolute',
    top: '-40%',
    right: '-35%',
    background: 'radial-gradient(at center center, rgb(92, 105, 255) 0%, rgba(rgb(163, 35, 255) 64%)',
  },
  '&:after': {
    content: '""',
    width: '140%',
    height: '140%',
    position: 'absolute',
    bottom: '-55%',
    left: '-30%',
    background: 'radial-gradient(at center center, rgb(255 0 33) 0%, rgba(247, 237, 225, 0) 70%)',
    transform: 'rotate(30deg)',
    zIndex: 1,
  },
})

type Props = {
  children: ReactNode
}

const AuthLayout: FC<Props> = ({ children }: Props) => (
  <Box sx={{ height: '100vh' }}>
    <Box sx={{ position: 'relative' }}>{children}</Box>
    <WallPaper />
  </Box>
)

export default AuthLayout
