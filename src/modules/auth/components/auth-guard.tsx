import LoginScreen from '@/modules/auth/screens/login.screen'
import { FC, ReactNode, useEffect, useState } from 'react'
import { Navigate, useLocation } from 'react-router-dom'

// hooks
import { useAuth } from '@/modules/auth/hooks'
import { useAppDispatch } from '@/plugins/redux'

interface Props {
  children: ReactNode
}

const AuthenticationGuard: FC<Props> = ({ children }) => {
  const dispatch = useAppDispatch()
  const { isAuthenticated, auth_fetchAuthUser } = useAuth()
  const { pathname } = useLocation()
  const [requestedLocation, setRequestedLocation] = useState<string | null>(null)

  useEffect(() => {
    dispatch(auth_fetchAuthUser()).unwrap()
  }, [pathname])

  if (!isAuthenticated) {
    if (pathname !== requestedLocation) {
      setRequestedLocation(pathname)
    }
    return <LoginScreen />
  }

  if (requestedLocation && pathname !== requestedLocation) {
    setRequestedLocation(null)
    return <Navigate to={requestedLocation} />
  }

  return <>{children}</>
}

export default AuthenticationGuard
