import Visibility from '@mui/icons-material/Visibility'
import VisibilityOff from '@mui/icons-material/VisibilityOff'
import Box from '@mui/material/Box'
import FormControl from '@mui/material/FormControl'
import IconButton from '@mui/material/IconButton'
import InputAdornment from '@mui/material/InputAdornment'
import InputLabel from '@mui/material/InputLabel'
import OutlinedInput from '@mui/material/OutlinedInput'
import TextField from '@mui/material/TextField'
import Grid from '@mui/material/Grid'
import { FC, useState } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { useAuth } from '../hooks'
import { StyledButton } from '@/modules/app/components/core'
import { IAuthSignInRequest } from '../api'
import { useAppDispatch } from '@/plugins/redux'
import { appConfig } from '@/modules/app/configs'
import AbsoluteFillLoading from '@/modules/app/components/absolute-fill-loading'

type TForm = IAuthSignInRequest

const defaultValues = {
  email: '',
  password: '',
}

const LoginForm: FC = () => {
  const dispatch = useAppDispatch()

  const [isLoading, setIsLoading] = useState(false)
  const { auth_signIn } = useAuth()

  const { control, handleSubmit } = useForm({
    defaultValues,
  })

  const [showPassword, setShowPassword] = useState<boolean>(false)

  const mailHost = `@${appConfig.appDomain}`

  const onSubmit: SubmitHandler<TForm> = values => {
    try {
      setIsLoading(true)
      dispatch(
        auth_signIn({
          ...values,
          email: values.email.includes(mailHost) ? values.email : values.email + mailHost,
        })
      )
    } catch (e) {
      setIsLoading(false)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClickShowPassword = (): void => {
    setShowPassword(!showPassword)
  }

  return (
    <Box
      sx={{
        position: 'relative',
        display: 'flex',
        fontWeight: 'bold',
        alignItems: 'center',
        flexDirection: 'column',
        justifyContent: 'center',
        width: '100%',
      }}
      component='form'
      onSubmit={handleSubmit(onSubmit)}
    >
      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }}>
          <Controller
            name='email'
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                autoComplete='off'
                fullWidth
                size='medium'
                margin='normal'
                label='Email'
                InputProps={{
                  endAdornment: <InputAdornment position='end'>{mailHost}</InputAdornment>,
                }}
              />
            )}
          />
        </Grid>
        <Grid size={{ xs: 12 }}>
          <Controller
            name='password'
            control={control}
            render={({ field }) => (
              <FormControl variant='outlined' fullWidth size='medium'>
                <InputLabel htmlFor='outlined-adornment-password'>Password</InputLabel>
                <OutlinedInput
                  {...field}
                  autoComplete='off'
                  size='medium'
                  fullWidth
                  type={showPassword ? 'text' : 'password'}
                  endAdornment={
                    <InputAdornment position='end'>
                      <IconButton onClick={handleClickShowPassword} edge='end'>
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  }
                  label='Password'
                />
              </FormControl>
            )}
          />
        </Grid>
        <Grid size={{ xs: 12 }}>
          <Box sx={{ mb: 2 }} />
          <StyledButton
            size='large'
            color='primary'
            variant='contained'
            type='submit'
            isLoading={isLoading}
            disableHoverEffect
            fullWidth
            sx={{ borderRadius: 3 }}
          >
            Sign In
          </StyledButton>
        </Grid>
      </Grid>
      {isLoading && <AbsoluteFillLoading />}
    </Box>
  )
}

export default LoginForm
