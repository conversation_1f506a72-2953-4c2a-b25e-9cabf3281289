import { useMemo } from 'react'

import * as auth_actionThunk from '@/modules/auth/slices/auth.thunk'
import { auth_selector, auth_reducerActions } from '@/modules/auth/slices'

import { useAppSelector } from '@/plugins/redux'

export const useAuth = () => {
  const state = useAppSelector(auth_selector)

  const isAuthenticated = useMemo<boolean>(() => {
    return Boolean(state.user && state.token)
  }, [state.user])

  return {
    isAuthenticated,
    ...state,
    ...auth_reducerActions,
    ...auth_actionThunk,
  }
}
