import axiosInstance from '@/plugins/axios/axios-instance'
import toast from 'react-hot-toast'

// store
import { store } from '@/plugins/redux/store'

export interface IAuthSignInRequest {
  email: string
  password: string
}

export interface IAuthResponseSignIn {
  token: string
  user: IUser
}

export const AuthApi = {
  signIn: async (body: IAuthSignInRequest): Promise<IAuthResponseSignIn | void> => {
    try {
      const response = await axiosInstance.post('/auth/login', body)
      return response?.data
    } catch (error) {
      toast.error('Invalid credentials.', { duration: 3000 })
    }
  },

  fetchAuthUser: async (): Promise<IUser> => {
    const response = await axiosInstance.get('/auth/user')
    return response.data
  },

  revokeToken: async (): Promise<IUser> => {
    const authToken = store.getState().auth?.token
    const response = await axiosInstance.post('/auth/revoke-token', { currentAccessToken: authToken })
    return response.data
  },
}
