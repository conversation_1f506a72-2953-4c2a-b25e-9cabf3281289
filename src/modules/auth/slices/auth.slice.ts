// redux toolkit
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import { auth_signIn, auth_fetchAuthUser, auth_revokeToken } from './auth.thunk'

// type for our state
export type IAuthSlice = {
  token: string | null
  user: IUser | null
  signIn_isLoading: boolean
  checkingAuthUserIsLoading: boolean
}

// initial state
export const auth_initialState: IAuthSlice = {
  token: null,
  user: null,
  signIn_isLoading: false,
  checkingAuthUserIsLoading: false,
}

export const authSlice = createSlice({
  name: 'auth',
  initialState: auth_initialState,
  reducers: {
    auth_setUser(state, action: PayloadAction<null>) {
      state.user = action.payload
    },
    auth_setToken(state, action: PayloadAction<string | null>) {
      state.token = action.payload
    },
    auth_setSignInIsLoading(state, action: PayloadAction<boolean>) {
      state.signIn_isLoading = action.payload
    },
    auth_reset: () => auth_initialState,
  },
  extraReducers: builder => {
    builder.addCase(auth_signIn.pending, (state, _) => {
      state.signIn_isLoading = true
    })
    builder.addCase(auth_signIn.rejected, (state, _) => {
      state.signIn_isLoading = false
      state.token = null
      state.user = null
    })
    builder.addCase(auth_signIn.fulfilled, (state, action) => {
      state.signIn_isLoading = false
      if (action.payload.token && action.payload?.user) {
        state.token = action.payload.token
        state.user = action.payload.user
        setTimeout(() => {
          window.location.href = '/'
        }, 350)
      }
    })

    builder.addCase(auth_fetchAuthUser.pending, (state, _) => {
      state.checkingAuthUserIsLoading = true
    })
    builder.addCase(auth_fetchAuthUser.rejected, (state, _) => {
      state.checkingAuthUserIsLoading = false
      state.token = null
      state.user = null
    })
    builder.addCase(auth_fetchAuthUser.fulfilled, (state, action) => {
      state.checkingAuthUserIsLoading = false
      if (action.payload?.id) {
        state.user = action.payload
      }
    })

    // builder.addCase(auth_revokeToken.pending, (state, _) => {})
    builder.addCase(auth_revokeToken.rejected, (state, _) => {
      state.token = null
      state.user = null
      state.signIn_isLoading = false
      state.checkingAuthUserIsLoading = false
    })
    builder.addCase(auth_revokeToken.fulfilled, (state, _) => {
      state.token = null
      state.user = null
      state.signIn_isLoading = false
      state.checkingAuthUserIsLoading = false
    })
  },
})

export const auth_reducerActions = authSlice.actions

export const auth_selector = (state: RootState): IAuthSlice => state.auth
