import { createAsyncThunk } from '@reduxjs/toolkit'
import { AuthApi, IAuthResponseSignIn, IAuthSignInRequest } from '../api'

export const auth_signIn = createAsyncThunk('@auth/signIn', async (body: IAuthSignInRequest): Promise<IAuthResponseSignIn> => {
  try {
    // @ts-ignore
    return await AuthApi.signIn(body)
  } catch (e) {
    const error = e as IApiResponseError
    // @ts-ignore
    return error
  }
})

export const auth_fetchAuthUser = createAsyncThunk('@auth/fetchAuthUser', async (): Promise<IUser> => {
  return await AuthApi.fetchAuthUser()
})

export const auth_revokeToken = createAsyncThunk('@auth/revokeToken', async (): Promise<IUser> => {
  return await AuthApi.revokeToken()
})
