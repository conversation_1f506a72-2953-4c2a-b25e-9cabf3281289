import axiosInstance from '@/plugins/axios/axios-instance'

export interface IMasterContactQueryParams extends IBasePaginateRequest {
  query?: string | null
}

export interface IStoreMasterContactRequest {
  player_id?: string
  phone_number: string
  notes?: string
}

export const MasterContactApi = {
  fetchList: async (_params: IMasterContactQueryParams): Promise<IPaginateResponse<IMasterContact>> => {
    const params: IMasterContactQueryParams = {
      // @ts-ignore
      paginate: 1,
      ..._params,
    }
    const response = await axiosInstance.get('/master-contact', { params })
    return response?.data
  },

  create: async (body: IStoreMasterContactRequest): Promise<IMasterContact> => {
    const response = await axiosInstance.post('/master-contact', body)
    return response?.data
  },

  import: async (file: File): Promise<IBaseApiResponse<unknown>> => {
    const formData = new FormData()
    formData.append('file', file)
    const response = await axiosInstance.post('/master-contact/import', formData)
    return response?.data
  },

  delete: async (userId: number): Promise<IBaseApiResponse<unknown>> => {
    const response = await axiosInstance.delete(`/master-contact/${userId}`)
    return response.data
  },

  deleteBulk: async (ids: number[]): Promise<IBaseApiResponse<unknown>> => {
    const response = await axiosInstance.post(`/master/contact/bulk-delete`, { ids })
    return response.data
  },
}
