declare global {
  interface IMasterContactTag {
    id: string
    name: string
  }
  interface IMasterContact {
    id: number
    player_id: string
    country_code: string
    phone_number: string
    telemarketing_id: number | null
    telemarketing?: IUser
    createdBy?: IUser
    importedBy?: IUser
    member_id: number
    notes: sting | null
    status: number
    mark_has_chat: boolean
    mark_has_chat_by: number | null
    mark_has_whatsapp: boolean
    mark_has_whatsapp_by: number | null
    maskHasWhatsAppBy: IUser | null
    markHasChatBy: IUser | null
    assigned_at: string | null
    tags?: MasterContactTag[]
    created_at: string // Dates are typically strings in ISO format from APIs
    updated_at: string // Dates are typically strings in ISO format from APIs
    _created_at: string // formatted GMT+7
    _updated_at: string // formatted GMT+7
    imported_by: number | null
    created_by: number | null
    telemarketing: number | null
    claim: IClaim | null
  }
}

export {}
