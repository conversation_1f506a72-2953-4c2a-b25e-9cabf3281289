// redux toolkit
import { createSlice } from '@reduxjs/toolkit'

import { masterContact_fetchList } from './master-contact.thunk'
import { IMasterContactQueryParams } from '../api/master-contact.api'
import { ROWS_PER_PAGE_OPTIONS } from '@/modules/app/constants'

// type for our state
export type IMasterContactSlice = {
  masterContact_listData: IMasterContact[]
  masterContact_listLoading: boolean
  masterContact_paginate: IPaginateMeta
  masterContact_queryParams: IMasterContactQueryParams
}

// initial state
export const masterContact_initialState: IMasterContactSlice = {
  masterContact_listData: [],
  masterContact_listLoading: false,
  masterContact_paginate: {
    current_page: 1,
    from: 0,
    per_page: 0,
    to: 0,
    total: 0,
  },
  masterContact_queryParams: {
    paginate: 1,
    perPage: ROWS_PER_PAGE_OPTIONS[0],
    page: 1,
    query: '',
  },
}

export const masterContactSlice = createSlice({
  name: 'master_contact',
  initialState: masterContact_initialState,
  reducers: {
    masterContact_reset: () => masterContact_initialState,
  },
  extraReducers: builder => {
    builder.addCase(masterContact_fetchList.pending, (state, _) => {
      state.masterContact_listLoading = true
    })
    builder.addCase(masterContact_fetchList.rejected, (state, _) => {
      state.masterContact_listLoading = false
    })
    builder.addCase(masterContact_fetchList.fulfilled, (state, action) => {
      state.masterContact_listLoading = false

      if (Array.isArray(action.payload.data)) {
        state.masterContact_listData = action.payload.data
      }

      if (action.payload.meta) {
        state.masterContact_paginate = action.payload.meta

        state.masterContact_queryParams.paginate = 1
        state.masterContact_queryParams.page = action.payload.meta.current_page
        state.masterContact_queryParams.perPage = action.payload.meta.per_page
      }

      // get query
      if (action.meta?.arg?.query) {
        state.masterContact_queryParams.query = action.meta?.arg?.query
        // state.masterContact_queryParams.query = action.meta.arg.query
      }
    })
  },
})

export const masterContact_reducerActions = masterContactSlice.actions

export const masterContact_selector = (state: RootState): IMasterContactSlice => state.master_contact
