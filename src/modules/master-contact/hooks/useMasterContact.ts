import * as auth_actionThunk from '@/modules/master-contact/slices/master-contact.thunk'
import { masterContact_selector, masterContact_reducerActions } from '@/modules/master-contact/slices'

import { useAppSelector } from '@/plugins/redux'

export const useMasterContact = () => {
  const state = useAppSelector(masterContact_selector)

  return {
    ...state,
    ...masterContact_reducerActions,
    ...auth_actionThunk,
  }
}
