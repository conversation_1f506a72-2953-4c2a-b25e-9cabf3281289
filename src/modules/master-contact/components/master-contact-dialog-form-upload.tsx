import { useState, FormEvent, Fragment, useCallback, FC, memo } from 'react'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Button from '@mui/material/Button'
import Box from '@mui/material/Box'
import Paper from '@mui/material/Paper'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import DialogTitle from '@mui/material/DialogTitle'
import ExportIcon from '@/assets/icons/lets-icons--export.svg?react'
import { MasterContactApi } from '../api/master-contact.api'
import AbsoluteFillLoading from '@/modules/app/components/absolute-fill-loading'
import DraggableFileUploader from '@/modules/app/components/draggable-file-uploader'
import { Swal2 } from '@/plugins/sweetalert2'

// const maxSizeInMB = 100
// const acceptedFormats = ['.csv']

interface Props {
  onUploadSuccess: () => void
}

const MasterContactDialogFormUpload: FC<Props> = ({ onUploadSuccess }) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  // State to store the parsed CSV data (array of arrays, where first array is headers)
  const [csvData, setCsvData] = useState<string[][]>([])
  const [isLoading, setIsLoading] = useState(false)

  const [uploadInProgress, setUploadInProgress] = useState(false)

  const handleOpenDialog = () => {
    setIsDialogOpen(true)
  }

  const onClose = useCallback(() => {
    setIsDialogOpen(false)
    setCsvData([])
    setUploadInProgress(false)
  }, [isDialogOpen, csvData, uploadInProgress])

  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault()
    try {
      setIsLoading(true)
      await MasterContactApi.import(selectedFile as File)
    } catch (e) {
      return Promise.reject(e)
    } finally {
      onClose()
      onUploadSuccess()
      setIsLoading(false)
      setSelectedFile(null)
      setCsvData([])
      Swal2.fire({
        title: 'DB Berhasl di upload!',
        text: 'Tunggu beberapa saat sistem akan otomatis membagikannya ke telemaketing',
        icon: 'success',
      })
    }
  }

  const handleClose = () => {
    setSelectedFile(null) // Clear file on close
    onClose()
  }

  const handleClickBackdrop = useCallback(() => {
    if (csvData?.length > 0 || uploadInProgress || isLoading) return
    setIsDialogOpen(false)
  }, [isDialogOpen, csvData, uploadInProgress, isLoading])

  const onSelectCsvFile = useCallback(
    (file: File) => {
      setSelectedFile(file)
    },
    [selectedFile]
  )

  return (
    <Fragment>
      <Button
        onClick={handleOpenDialog}
        size={'small'}
        variant='text'
        startIcon={<Box component={ExportIcon} sx={{ height: 22, width: 22, transform: 'rotate(180deg)' }} />}
      >
        Import
      </Button>

      <Dialog
        open={isDialogOpen}
        onClose={handleClickBackdrop}
        aria-labelledby='form-dialog-import-db'
        maxWidth={csvData?.length > 0 ? 'sm' : 'xs'}
        fullWidth
        sx={{
          '& .MuiPaper-root': { borderRadius: 4 },
          '& .MuiDialog-paper': {
            boxShadow: 0,
          },
          transition: theme => theme.transitions.create(['width']),
        }}
      >
        <DialogTitle>Import Data Distribusi (Master Contact) (CSV)</DialogTitle>
        <DialogContent sx={{ px: 0, py: 0 }}>
          <Box
            component='form'
            onSubmit={handleSubmit}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
              alignItems: 'center',
            }}
          >
            <DraggableFileUploader
              isLoading={isLoading}
              selectedFile={selectedFile as File}
              onSelectFile={onSelectCsvFile}
              onRemoveFile={() => setSelectedFile(null)}
            />
            {/* CSV Data Table */}
            {csvData.length > 0 && (
              <TableContainer
                component={Paper}
                sx={{
                  border: theme => `1px solid ${theme.palette.divider}`,
                  width: '100%',
                  height: 260,
                  overflow: 'scroll',
                  borderRadius: '5px !important',
                }}
              >
                <Table stickyHeader size='small' aria-label='csv data table'>
                  <TableHead>
                    <TableRow className='bg-blue-100'>
                      {/* Render table headers from the first row of CSV data */}
                      {csvData[0].map((header, index) => (
                        <TableCell
                          key={index}
                          className='font-bold text-blue-800 text-lg py-3'
                          sx={{ backgroundColor: 'primary.main', color: 'primary.contrastText', fontWeight: 500 }}
                        >
                          {header ?? '-'}
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {/* Render table rows starting from the second row (data rows) */}
                    {csvData.slice(1).map((row, rowIndex) => (
                      <TableRow key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                        {row.map((cell, cellIndex) => (
                          <TableCell key={cellIndex} className='text-gray-800 py-2'>
                            {cell ?? '-'}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color='error' disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} color='primary' disabled={!selectedFile || isLoading}>
            Upload
          </Button>
        </DialogActions>
        {isLoading && <AbsoluteFillLoading />}
      </Dialog>
    </Fragment>
  )
}

export default memo(MasterContactDialogFormUpload)
