import Box from '@mui/material/Box'
import MasterContactTable from '../components/master-contact-table'
import { Fragment, useCallback, useEffect } from 'react'
import { useAppDispatch } from '@/plugins/redux'
import { useMasterContact } from '../hooks'

// icons
import BookBookmarkIcon from '@/assets/icons/solar--book-bookmark-bold-duotone.svg?react'
import MasterContactToolbar from '../components/master-contact-toolbar'
import { IMasterContactQueryParams } from '../api/master-contact.api'
import PageHeader from '@/modules/app/components/page-header'
import { CircularProgress } from '@mui/material'

const MasterContactScreen = () => {
  const dispatch = useAppDispatch()

  const { masterContact_fetchList, masterContact_listData, masterContact_listLoading, masterContact_paginate, masterContact_queryParams } =
    useMasterContact()

  const refetchData = useCallback(
    (params: Partial<IMasterContactQueryParams>) => {
      dispatch(masterContact_fetchList({ ...masterContact_queryParams, ...params }))
    },
    [masterContact_queryParams]
  )

  useEffect(() => {
    refetchData(masterContact_queryParams)
  }, [])

  return (
    <Box>
      <PageHeader title='Database Contact' subtitle='  Manage database contact list' icon={BookBookmarkIcon} />
      <Box sx={{ px: 3, pt: 5 }}>
        <Box sx={{ backgroundColor: 'background.paper', px: 2, pb: 0.3, pt: 1.5, borderRadius: 2 }}>
          <MasterContactToolbar />
          {masterContact_listLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Fragment>
              <MasterContactTable rows={masterContact_listData} paginate={masterContact_paginate} fetchData={refetchData} />
            </Fragment>
          )}
        </Box>
      </Box>
    </Box>
  )
}

export default MasterContactScreen
