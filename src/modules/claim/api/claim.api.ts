import axiosInstance from '@/plugins/axios/axios-instance'
import { ClaimTypes } from '../constants'

export interface IClaimQueryParams extends IBasePaginateRequest {
  query?: string | null
  telemarketing_id: number | null
  type: ClaimTypes | null
}

export interface IApproveClaimRegistrationRequest {
  claimId: number
  telemarketingId: number
  playerId: string | null
  phone_number: string | null
}

export const ClaimApi = {
  fetchListClaimRegistration: async (params: IClaimQueryParams): Promise<IPaginateResponse<IClaim>> => {
    const response = await axiosInstance.get('/admin/claim/registration', { params })
    return response?.data
  },
  fetchListClaimFirstDeposit: async (params: IClaimQueryParams): Promise<IPaginateResponse<IClaim>> => {
    const response = await axiosInstance.get('/admin/claim/fd', { params })
    return response?.data
  },
  approveClaimRegistration: async (body: IApproveClaimRegistrationRequest): Promise<IClaim> => {
    const response = await axiosInstance.post('/claim/approve/registration', body)
    return response?.data
  },
  approveClaimFirstDeposit: async (body: IApproveClaimRegistrationRequest): Promise<IClaim> => {
    const response = await axiosInstance.post('/claim/approve/fd', body)
    return response?.data
  },
}
