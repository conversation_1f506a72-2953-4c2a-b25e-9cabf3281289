import { createAsyncThunk } from '@reduxjs/toolkit'
import { ClaimApi, IClaimQueryParams } from '../api/claim.api'

export const claim_fetchListClaimRegistration = createAsyncThunk(
  '@claim/fetchListClaimRegistration',
  async (params: IClaimQueryParams): Promise<IPaginateResponse<IClaim>> => {
    return await ClaimApi.fetchListClaimRegistration(params)
  }
)

export const claim_fetchListClaimFirstDeposit = createAsyncThunk(
  '@claim/fetchListClaimFirstDeposit',
  async (params: IClaimQueryParams): Promise<IPaginateResponse<IClaim>> => {
    return await ClaimApi.fetchListClaimFirstDeposit(params)
  }
)
