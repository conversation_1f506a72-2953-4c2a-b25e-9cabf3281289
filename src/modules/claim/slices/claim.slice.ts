// redux toolkit
import { createSlice } from '@reduxjs/toolkit'

import { claim_fetchListClaimRegistration, claim_fetchListClaimFirstDeposit } from './claim.thunk'
import { IClaimQueryParams } from '../api/claim.api'
import { ClaimTypes } from '../constants'

// type for our state
export type IClaimSlice = {
  claim_regisListData: IClaim[]
  claim_regisLoading: boolean
  claim_regisPagination: IPaginateMeta
  claim_dfListData: IClaim[]
  claim_dfLoading: boolean
  claim_dfPagination: IPaginateMeta
  claim_registerQueryParams: IClaimQueryParams
  claim_firstDepositQueryParams: IClaimQueryParams
}

// initial state
export const claim_initialState: IClaimSlice = {
  claim_regisListData: [],
  claim_regisLoading: false,
  claim_regisPagination: {
    current_page: 1,
    from: 0,
    per_page: 0,
    to: 0,
    total: 0,
  },
  claim_dfListData: [],
  claim_dfLoading: false,
  claim_dfPagination: {
    current_page: 1,
    from: 0,
    per_page: 0,
    to: 0,
    total: 0,
  },
  claim_registerQueryParams: {
    paginate: 1,
    perPage: 250,
    page: 1,
    telemarketing_id: null,
    type: ClaimTypes.REGISTER,
    query: null,
  },
  claim_firstDepositQueryParams: {
    paginate: 1,
    perPage: 250,
    page: 1,
    telemarketing_id: null,
    type: ClaimTypes.FIRST_DEPOSIT,
    query: null,
  },
}

export const claimSlice = createSlice({
  name: 'claim',
  initialState: claim_initialState,
  reducers: {
    claim_reset: () => claim_initialState,
  },
  extraReducers: builder => {
    // fetch list claim regis
    builder.addCase(claim_fetchListClaimRegistration.pending, (state, _) => {
      state.claim_regisLoading = true
    })
    builder.addCase(claim_fetchListClaimRegistration.rejected, (state, _) => {
      state.claim_regisLoading = false
    })
    builder.addCase(claim_fetchListClaimRegistration.fulfilled, (state, action) => {
      state.claim_regisLoading = false
      if (Array.isArray(action.payload.data)) {
        state.claim_regisListData = action.payload.data
      }
      if (action.payload.meta) {
        state.claim_regisPagination = action.payload.meta

        state.claim_registerQueryParams.paginate = 1
        state.claim_registerQueryParams.page = action.payload.meta.current_page
        state.claim_registerQueryParams.perPage = action.payload.meta.per_page
      }
    })

    builder.addCase(claim_fetchListClaimFirstDeposit.pending, (state, _) => {
      state.claim_dfLoading = true
    })
    builder.addCase(claim_fetchListClaimFirstDeposit.rejected, (state, _) => {
      state.claim_dfLoading = false
    })
    builder.addCase(claim_fetchListClaimFirstDeposit.fulfilled, (state, action) => {
      state.claim_dfLoading = false
      if (Array.isArray(action.payload.data)) {
        state.claim_dfListData = action.payload.data
      }
      if (action.payload.meta) {
        state.claim_dfPagination = action.payload.meta

        state.claim_firstDepositQueryParams.paginate = 1
        state.claim_firstDepositQueryParams.page = action.payload.meta.current_page
        state.claim_firstDepositQueryParams.perPage = action.payload.meta.per_page
      }
    })
  },
})

export const claim_reducerActions = claimSlice.actions

export const claim_selector = (state: RootState): IClaimSlice => state.claim
