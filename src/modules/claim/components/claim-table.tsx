import { FC, memo, useCallback, useState } from 'react'
import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import Paper from '@mui/material/Paper'
import Button from '@mui/material/Button'
import Avatar from '@mui/material/Avatar'
import ButtonGroup from '@mui/material/ButtonGroup'
import Typography from '@mui/material/Typography'
import { DataGrid, GridColDef } from '@mui/x-data-grid'

import ClockIcon from '@/assets/icons/iconamoon--clock-fill.svg?react'
import CheckedOutlineIcon from '@/assets/icons/lets-icons--check-fill.svg?react'
import ProgressIcon from '@/assets/icons/ri--progress-5-line.svg?react'
import ArrowUpIcon from '@/assets/icons/mdi--arrow-up.svg?react'

// dayjs
import dayjs from 'dayjs'

// toast
import toast from 'react-hot-toast'

// assets
import AvatarImg from '@/assets/avatars/avatar_8.jpg'
import { MasterContactStatus } from '@/modules/master-contact/constants/master-contact.constant'
import { TablePagination } from '@mui/material'
import { IMasterContactQueryParams, MasterContactApi } from '@/modules/master-contact/api/master-contact.api'
import { ROWS_PER_PAGE_OPTIONS } from '@/modules/app/constants'
import DialogConfirm from '@/modules/app/components/dialog-confirm'

const getStatusColor = (status: MasterContactStatus) => {
  switch (status) {
    case MasterContactStatus.UNASSIGNED:
      return '#ff6c47'
    case MasterContactStatus.PROCESSING:
      return '#4768fc'
    case MasterContactStatus.CLAIM_REGIS:
      return '#c244cb'
    case MasterContactStatus.CLAIM_FD:
      return '#00b91f'
    case MasterContactStatus.COMPLETED:
      return '#009f5f'
    default:
      return '#626262'
  }
}

const getStatusIcon = (status: MasterContactStatus) => {
  switch (status) {
    case MasterContactStatus.UNASSIGNED:
      return ClockIcon
    case MasterContactStatus.PROCESSING:
      return ProgressIcon
    case MasterContactStatus.COMPLETED:
      return CheckedOutlineIcon
    default:
      return ClockIcon
  }
}

const getStatusName = (status: MasterContactStatus) => {
  switch (status) {
    case MasterContactStatus.UNASSIGNED:
      return 'Unassigned'
    case MasterContactStatus.PROCESSING:
      return 'Signed'
    case MasterContactStatus.CLAIM_REGIS:
      return 'Claim Regis'
    case MasterContactStatus.CLAIM_FD:
      return 'Claim FD'
    case MasterContactStatus.COMPLETED:
      return 'Completed'
    default:
      return 'Unknown'
  }
}

interface Props {
  rows: IMasterContact[]
  paginate: IPaginateMeta
  fetchData: (params: Partial<IMasterContactQueryParams>) => void
}

const MasterContactTable: FC<Props> = ({ rows, paginate, fetchData }) => {
  const [deleteId, setDeleteId] = useState<number | null>(null)

  const handleChangePage = (_: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    fetchData({ page: newPage + 1 })
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    fetchData({ page: 1, perPage: parseInt(event.target.value, 10) })
  }

  const onClickDelete = (id: number) => {
    setDeleteId(id)
  }

  const onConfirmDelete = useCallback(async (itemId?: number) => {
    try {
      const response = await MasterContactApi.delete(itemId as number)
      if (response) {
        handleCloseDialogConfirmDelete()
        toast.success('Master contact deleted.', { duration: 3000, position: 'bottom-center' })
        fetchData({ page: paginate.current_page })
      }
    } catch (e) {
      console.log('e', e)
      toast.error('Failed to delete master contact.', { duration: 3000, position: 'bottom-center' })
    }
  }, [])

  const handleCloseDialogConfirmDelete = useCallback(() => {
    setDeleteId(null)
  }, [deleteId])

  const columns: GridColDef<(typeof rows)[number]>[] = [
    {
      field: 'id',
      headerName: 'ID',
      sortable: true,
      width: 90,
      renderCell: params => (
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            cursor: 'pointer',
            '&:hover': {
              color: 'primary.main',
              textDecoration: 'underline',
            },
          }}
        >
          <Typography sx={{ fontWeight: 500, fontSize: '0.8rem', lineHeight: 1, color: 'inherit' }}>{params?.value}</Typography>
          <Box component={ArrowUpIcon} sx={{ height: 17, width: 17, color: 'inherit', ml: 0.4, transform: 'rotate(45deg)' }} />
        </Box>
      ),
    },
    {
      field: 'phone_number',
      sortable: true,
      headerName: 'Phone Number',
      description: 'This column has a value getter and is not sortable.',
      width: 160,
      valueGetter: (_, row) => `${row.country_code || ''}  ${row.phone_number || ''}`,
    },
    {
      field: 'name',
      headerName: 'Name',
      width: 100,
      sortable: true,
      editable: false,
      valueGetter: (_, row) => `${row.player_id || '-'}`,
    },
    {
      field: 'status',
      headerName: 'Status',
      sortable: true,
      width: 132,
      renderCell: params => (
        <Box sx={{ height: '100%', flexDirection: 'row', display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box component={getStatusIcon(params.value)} sx={{ height: 16, width: 16, color: getStatusColor(params.value) }} />
          <Typography sx={{ fontSize: '0.8rem', lineHeight: 1, color: getStatusColor(params.value), fontWeight: '400' }}>
            {getStatusName(params.value)}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'created_by',
      headerName: 'Created by',
      width: 150,
      editable: false,
      renderCell: params => {
        if (params?.row?.createdBy) {
          return (
            <Stack direction='row' spacing={1} alignItems='center' sx={{ height: '100%' }}>
              <Avatar alt='User Avatar' src={AvatarImg} sx={{ width: 20, height: 20 }} />
              <Box>
                <Typography sx={{ fontWeight: '400', fontSize: '0.85rem' }}>{params?.row?.createdBy?.name}</Typography>
              </Box>
            </Stack>
          )
        }
        return '-'
      },
    },
    {
      field: 'imported_by',
      headerName: 'Import by',
      type: 'number',
      width: 160,
      editable: false,
      headerAlign: 'left',
      align: 'left',
      renderCell: params => {
        if (params?.row?.importedBy) {
          return (
            <Stack direction='row' spacing={1} alignItems='center' sx={{ height: '100%' }}>
              <Avatar alt='User Avatar' src={AvatarImg} sx={{ width: 20, height: 20 }} />
              <Box>
                <Typography sx={{ fontWeight: '400', fontSize: '0.85rem' }}>{params?.row?.importedBy?.name}</Typography>
              </Box>
            </Stack>
          )
        }
        return '-'
      },
    },
    {
      field: 'telemarketing_id',
      headerName: 'Upline',
      width: 160,
      editable: false,
      headerAlign: 'left',
      renderCell: params => {
        if (!params.row?.telemarketing) return '-'
        return (
          <Stack direction='row' spacing={1} alignItems='center' sx={{ height: '100%' }}>
            <Avatar alt='User Avatar' src={AvatarImg} sx={{ width: 20, height: 20 }} />
            <Box>
              <Typography sx={{ fontWeight: '400', fontSize: '0.85rem' }}>{params?.row?.telemarketing?.name}</Typography>
            </Box>
          </Stack>
        )
      },
    },
    {
      field: 'created_at',
      headerName: 'Created at',
      width: 120,
      editable: false,
      align: 'left',
      renderCell: params => (
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'center' }}>
          <Typography sx={{ fontWeight: '400', fontSize: '0.8rem', lineHeight: 1.5 }}>
            {dayjs(params?.row?.created_at).format('DD MMM YYYY')}
          </Typography>
          <Typography sx={{ fontWeight: '400', fontSize: '0.7rem', color: 'text.secondary', lineHeight: 1.2 }}>
            {dayjs(params?.row?.created_at).format('HH:mm')} WIB
          </Typography>
        </Box>
      ),
    },
    {
      field: 'country_code',
      headerName: 'Actions',
      width: 200,
      sortable: false,
      editable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <ButtonGroup size='small' aria-label='filter view group btn'>
          <Button variant='outlined' size='small' color='error' onClick={() => onClickDelete(params.row.id)}>
            Delete
          </Button>
          <Button variant='outlined' size='small' color='info'>
            Edit
          </Button>
          <Button variant='outlined' size='small' color='success'>
            View
          </Button>
        </ButtonGroup>
      ),
    },
  ]

  return (
    <Paper
      elevation={0}
      sx={{
        p: 0,
        width: '100%',
        '& .MuiDataGrid-columnHeadersContainer': {
          backgroundColor: 'background.paper',
        },
      }}
    >
      <DataGrid
        rowHeight={52}
        rows={rows}
        columns={columns}
        checkboxSelection
        sx={{
          backgroundColor: 'background.paper',
          borderColor: 'divider',
          '& .MuiDataGrid-cell': {
            fontSize: '0.9rem',
            fontWeight: '400',
          },
          '& .MuiDataGrid-columnHeaderTitle': {
            fontWeight: 500,
            fontSize: '0.9rem',
          },
        }}
        hideFooter
        disableRowSelectionOnClick
      />
      <TablePagination
        component='div'
        count={paginate.total}
        page={paginate.current_page - 1}
        onPageChange={handleChangePage}
        rowsPerPage={paginate.per_page}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[...ROWS_PER_PAGE_OPTIONS]}
      />

      <DialogConfirm
        itemId={deleteId as number}
        open={Boolean(deleteId)}
        onCancel={handleCloseDialogConfirmDelete}
        onConfirm={onConfirmDelete}
        title='Are you sure?'
        subtitle="You won't be able to revert this!"
      />
    </Paper>
  )
}

export default memo(MasterContactTable)
