import { FC, memo, useMemo } from 'react'
import { Box, Typography } from '@mui/material'
import { ClaimUtils } from '../utilities'
import { ClaimTypes } from '../constants'

interface Props {
  type: ClaimTypes
}

const ClaimTypeLabel: FC<Props> = ({ type }) => {
  const color = useMemo(() => {
    switch (type) {
      case ClaimTypes.FIRST_DEPOSIT:
        return '#01957f'
      case ClaimTypes.REGISTER:
        return '#843ca8'
      default:
        return '#525252'
    }
  }, [type])

  const backgroundColor = useMemo(() => {
    switch (type) {
      case ClaimTypes.FIRST_DEPOSIT:
        return '#d4fdf8'
      case ClaimTypes.REGISTER:
        return '#f7e7ff'
      default:
        return '#ffeeee'
    }
  }, [type])

  return (
    <Box
      sx={{
        top: 0,
        right: 0,
        px: 1.2,
        py: 0.4,
        pr: 0.7,
        borderRadius: 1,
        backgroundColor,
      }}
    >
      <Typography style={{ color, fontSize: 13, fontWeight: '600' }}>{ClaimUtils.getClaimTypeName(type)}</Typography>
    </Box>
  )
}

ClaimTypeLabel.displayName = 'ClaimTypeLabel'

export default memo(ClaimTypeLabel)
