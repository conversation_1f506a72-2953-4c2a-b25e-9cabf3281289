import { FC, memo, useCallback } from 'react'
import PhoneSvgIcon from '@/assets/icons/solar--phone-rounded-bold-duotone.svg?react'
import CheckIcon from '@/assets/icons/ep--success-filled.svg?react'
import { ClaimUtils } from '../utilities/claim.util'
import BaseLabelCard from '@/modules/app/components/base-label-card'
import dayjs from 'dayjs'
import Avatar from '@mui/material/Avatar'
import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { useTheme } from '@mui/material/styles'
import { AppUtils } from '@/modules/app/utils/app.util'
import ClaimTypeLabel from './claim-type-label'
import { ClaimTypes } from '../constants'

interface ClaimCardItemProps {
  index: number
  item: IClaim
  onClickItem: (item: IClaim) => void
}

const ClaimCardItem: FC<ClaimCardItemProps> = ({ item, onClickItem }) => {
  const theme = useTheme()

  const onClick = useCallback(() => {
    onClickItem(item)
    // console.log('ITEM', item)
  }, [item])

  return (
    <Stack
      onClick={onClick}
      sx={{
        backgroundColor: theme.palette.background.paper,
        borderRadius: 2,
        borderWidth: 2,
        borderColor: ClaimUtils.getStatusColor(item.status),
        borderStyle: 'solid',
        overflow: 'hidden',
        position: 'relative',
        boxShadow: 1,
        cursor: 'pointer',
      }}
    >
      <BaseLabelCard color={ClaimUtils.getStatusColor(item.status)} label={ClaimUtils.getName(item.status)} />

      <Box sx={{ px: 2, py: 1.2 }}>
        <Stack sx={{ display: 'inline-flex', mb: 1 }}>
          <ClaimTypeLabel type={item.type as ClaimTypes} />
        </Stack>
        <Stack
          direction='row'
          spacing={1}
          alignItems='center'
          sx={{
            height: '100%',
            borderBottom: `1px solid ${theme.palette.divider}`,
            mb: 1,
            pb: 1,
          }}
        >
          <Avatar alt='User Avatar' sx={{ width: 28, height: 28, bgcolor: item.telemarketing?.avatar_text_color, fontSize: 28 * 0.4 }}>
            {AppUtils.getFirstLetters(item?.telemarketing?.name ?? '-')}
          </Avatar>
          <Box>
            <Typography sx={{ fontSize: '0.85rem' }}>{item?.telemarketing?.name}</Typography>
            <Typography sx={{ fontWeight: '400', fontSize: '0.6rem' }}>{item?.telemarketing?.referral_code}</Typography>
          </Box>
        </Stack>
        <Box sx={{ borderBottomWidth: 1, borderColor: theme.palette.divider }}>
          <Stack direction='row' spacing={1} alignItems='center' sx={{ mb: 1 }}>
            <Typography sx={{ color: 'text.secondary' }}>User ID:</Typography>
            <Typography sx={{ fontWeight: '600', fontSize: 14 }}>{item.player_id}</Typography>
          </Stack>
          <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'flex', justifyContent: 'flex-start' }}>
            <Box component={PhoneSvgIcon} sx={{ height: 18, width: 18 }} />
            <Typography sx={{ fontWeight: '500', fontSize: 12 }}>
              {item.phone_number ? `+${item.country_code}${item.phone_number}` : `+${item.country_code}${item?.phone_number ?? ''}`}
            </Typography>
          </Box>
        </Box>
        <Box sx={{ mt: 1.2 }}>
          <Box sx={{ display: 'flex', flexDirection: 'row', gap: 1, alignItems: 'center' }}>
            <Typography sx={{ fontSize: 11 }}>Tanggal klaim:</Typography>
            <Typography sx={{ fontSize: 11 }}>{dayjs(item.date).format('DD MMM YYYY HH:mm')}</Typography>
          </Box>
          {item?.is_auto_claim && (
            <Box sx={{ display: 'flex', flexDirection: 'row', gap: 1, alignItems: 'center', mt: 1 }}>
              <Box
                sx={{
                  display: 'inline-flex',
                  backgroundColor: theme.palette.success.main,
                  flexDirection: 'row',
                  gap: 1,
                  alignItems: 'center',
                  mt: 1,
                  borderRadius: 4,
                  px: 0.8,
                  py: 0.2,
                }}
              >
                <Box component={CheckIcon} sx={{ height: 14, width: 14, color: '#fff' }} />
                <Typography sx={{ fontSize: 12, color: '#fff' }}>Auto Klaim</Typography>
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Stack>
  )
}

export default memo(ClaimCardItem)
