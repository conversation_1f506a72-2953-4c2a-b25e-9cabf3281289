import { FC, useCallback, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Box, TextField, Typography, IconButton } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import AddIcon from '@/assets/icons/mingcute--add-fill.svg?react'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'

// api
import { IStoreMasterContactRequest, MasterContactApi } from '@/modules/master-contact/api/master-contact.api'

// 1. Define the Yup Validation Schema
const schema = yup
  .object({
    player_id: yup.string(),
    phone_number: yup.string().required('Phone number is required'),
    notes: yup.string(),
  })
  .required()

interface Props {
  onSaveSuccess: () => void
}

const MasterContactDrawerForm: FC<Props> = ({ onSaveSuccess }) => {
  const [open, setOpen] = useState(false)

  // Initialize react-hook-form
  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<IStoreMasterContactRequest>({
    defaultValues: {
      player_id: '',
      phone_number: '',
      notes: '',
    },
    // @ts-ignore
    resolver: yupResolver(schema),
  })

  const toggleDrawer = (newOpen: boolean) => () => {
    setOpen(newOpen)
  }

  const onValidSubmit: SubmitHandler<IStoreMasterContactRequest> = useCallback(async values => {
    console.log(values)
    try {
      const response = await MasterContactApi.create(values)
      if (response.id) {
        onSaveSuccess()
        reset()
        setOpen(false)
      }
    } catch (e) {
      //  setOpen()
    }
  }, [])

  return (
    <div>
      <Button
        variant='contained'
        onClick={toggleDrawer(true)}
        size='small'
        disableElevation
        startIcon={<Box component={AddIcon} sx={{ height: 16, width: 16 }} />}
      >
        Add New
      </Button>
      <Drawer
        anchor='right'
        open={open}
        onClose={toggleDrawer(false)}
        sx={{
          zIndex: 1300,
          mr: 4,
          '& .MuiPaper-root': {
            height: '100vh',
            backgroundColor: 'transparent !important',
            boxShadow: 0,
            backgroundImage: 'none',
          },
        }}
      >
        <Box
          sx={{
            my: 2,
            mr: 2,
            boxShadow: 0,
            borderRadius: 4,
            backgroundColor: 'background.paper',
            height: '100%',
          }}
        >
          <Box
            sx={{
              width: 392,
              px: 4,
              pt: 2,
              display: 'flex',
              flexDirection: 'column',
            }}
            role='presentation'
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 2 }}>
              <Typography variant='h6' component='h2'>
                Add New Contact
              </Typography>
              <IconButton onClick={toggleDrawer(false)}>
                <CloseIcon />
              </IconButton>
            </Box>
            {/* @ts-ignore */}
            <form onSubmit={handleSubmit(onValidSubmit)}>
              <Controller
                name='player_id'
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    size='medium'
                    label='Contact name'
                    variant='outlined'
                    fullWidth
                    error={!!errors.player_id}
                    helperText={errors.player_id ? errors.player_id.message : ''}
                    sx={{ mb: 3 }}
                  />
                )}
              />
              <Controller
                name='phone_number'
                control={control}
                rules={{ required: 'Phone number is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    size='medium'
                    label='Phone Number'
                    variant='outlined'
                    fullWidth
                    error={!!errors.phone_number}
                    helperText={errors.phone_number ? errors.phone_number.message : ''}
                    sx={{ mb: 2 }}
                  />
                )}
              />
              <Button disableElevation type='submit' size='large' fullWidth variant='contained' color='primary' sx={{ mt: 2 }}>
                Save
              </Button>
            </form>
          </Box>
        </Box>
      </Drawer>
    </div>
  )
}

export default MasterContactDrawerForm
