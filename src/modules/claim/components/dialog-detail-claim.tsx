import { Fragment, FC, memo } from 'react'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import { useTheme } from '@mui/material/styles'
import DialogTitle from '@mui/material/DialogTitle'
import IconButton from '@mui/material/IconButton'
import Avatar from '@mui/material/Avatar'
import Stack from '@mui/material/Stack'
import { ClaimStatuses, ClaimTypes } from '../constants'
import dayjs from 'dayjs'

import CheckIcon from '@/assets/icons/ep--success-filled.svg?react'
import WarningIcon from '@/assets/icons/fluent--warning-12-filled.svg?react'
import CloseIcon from '@/assets/icons/grommet-icons--form-close.svg?react'
import CloseFilledIcon from '@/assets/icons/ri--close-circle-fill.svg?react'
import { amber, green, red } from '@mui/material/colors'
import { AppUtils } from '@/modules/app/utils/app.util'
import ClaimTypeLabel from './claim-type-label'

interface Props {
  item: IClaim
  open: boolean
  onClose: () => void
  onApproveSuccess: (item: IClaim) => void
}

const DialogDetailClaim: FC<Props> = ({ item, open, onClose }) => {
  // const [isLoading, setIsLoading] = useState(false)
  const theme = useTheme()

  return (
    <Fragment>
      <Dialog
        open={open}
        onClose={onClose}
        aria-labelledby='form-dialog-import-db'
        maxWidth='sm'
        fullWidth
        sx={{
          '& .MuiPaper-root': { borderRadius: 4 },
          '& .MuiDialog-paper': {
            boxShadow: 0,
          },
        }}
      >
        <DialogTitle>
          Claim Information
          <IconButton onClick={onClose} sx={{ marginLeft: 'auto', position: 'absolute', top: 12, right: 12 }}>
            <Box component={CloseIcon} />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ mt: 2, px: 4 }}>
          {item?.id && (
            <Box sx={{}}>
              <Box sx={{ width: '100%' }}>
                <Box
                  sx={{
                    gap: 2,
                    marginBottom: 1.2,
                    pb: 1,
                    borderBottomColor: theme.palette.divider,
                    borderBottomWidth: 1,
                    borderBottomStyle: 'solid',
                  }}
                >
                  <Typography variant='subtitle2' color='text.secondary'>
                    Jenis:{' '}
                  </Typography>
                  <Stack sx={{ display: 'inline-flex', mt: 0.5 }}>
                    <ClaimTypeLabel type={item.type as ClaimTypes} />
                  </Stack>
                </Box>
                <Box
                  sx={{
                    gap: 2,
                    marginBottom: 1.2,
                    pb: 1,
                    borderBottomColor: theme.palette.divider,
                    borderBottomWidth: 1,
                    borderBottomStyle: 'solid',
                  }}
                >
                  <Typography variant='subtitle2' color='text.secondary'>
                    Tanggal Klaim:{' '}
                  </Typography>
                  <Typography variant='body1'>{dayjs(item.date).format('DD MMM YYYY HH:mm')} </Typography>
                </Box>
                <Box
                  sx={{
                    gap: 2,
                    marginBottom: 1.2,
                    pb: 1,
                    borderBottomColor: theme.palette.divider,
                    borderBottomWidth: 1,
                    borderBottomStyle: 'solid',
                  }}
                >
                  <Typography variant='subtitle2' color='text.secondary'>
                    User ID:{' '}
                  </Typography>
                  <Typography variant='body1'>{item?.player_id ?? '-'}</Typography>
                </Box>
                <Box
                  sx={{
                    gap: 2,
                    marginBottom: 1.2,
                    pb: 1,
                    borderBottomColor: theme.palette.divider,
                    borderBottomWidth: 1,
                    borderBottomStyle: 'solid',
                  }}
                >
                  <Typography variant='subtitle2' color='text.secondary'>
                    No HP:{' '}
                  </Typography>
                  <Typography variant='body1'>{item?.phone_number ? `${item.country_code} ${item.phone_number}` : '-'}</Typography>
                </Box>
                {item?.type === ClaimTypes.FIRST_DEPOSIT && (
                  <Box
                    sx={{
                      gap: 2,
                      marginBottom: 1.2,
                      pb: 1,
                      borderBottomColor: theme.palette.divider,
                      borderBottomWidth: 1,
                      borderBottomStyle: 'solid',
                    }}
                  >
                    {item.amount !== item.actual_amount ? (
                      <Fragment>
                        <Stack>
                          <Typography variant='subtitle2' color='text.secondary'>
                            Nominal Klaim:{' '}
                          </Typography>
                          <Typography variant='body1' sx={{ color: 'red', textDecoration: 'line-through' }}>
                            {AppUtils.formatToRupiah(item.amount)}
                          </Typography>
                        </Stack>
                        <Stack>
                          <Typography variant='subtitle2' color='text.secondary'>
                            Nominal:{' '}
                          </Typography>
                          <Typography variant='body1'>
                            {item?.actual_amount ? AppUtils.formatToRupiah(item?.actual_amount) : '-'}
                          </Typography>
                        </Stack>
                      </Fragment>
                    ) : (
                      <Fragment>
                        <Typography variant='subtitle2' color='text.secondary'>
                          Nominal Klaim:{' '}
                        </Typography>
                        <Typography variant='body1'>{item?.amount ? AppUtils.formatToRupiah(item?.amount) : '-'}</Typography>
                      </Fragment>
                    )}
                  </Box>
                )}
                <Box
                  sx={{
                    gap: 2,
                    marginBottom: 1.2,
                    paddingBottom: 2,
                    borderBottomColor: theme.palette.divider,
                    borderBottomWidth: 1,
                    borderBottomStyle: 'solid',
                  }}
                >
                  <Stack sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', gap: 1 }}>
                    <Stack>
                      <Typography variant='subtitle2' color='text.secondary'>
                        Diklaim oleh:{' '}
                      </Typography>
                      <Typography variant='body1'>{item?.telemarketing?.name}</Typography>
                    </Stack>
                    <Avatar
                      alt='User Avatar'
                      sx={{ width: 28, height: 28, bgcolor: item.telemarketing?.avatar_text_color, fontSize: 28 * 0.4 }}
                    >
                      {AppUtils.getFirstLetters(item?.telemarketing?.name ?? '-')}
                    </Avatar>
                  </Stack>
                </Box>
                <Box sx={{ gap: 2, paddingBottom: 2, borderBottomColor: theme.palette.divider, borderBottomWidth: 1 }}>
                  <Typography variant='subtitle2' color='text.secondary'>
                    Catatan klaim:{' '}
                  </Typography>
                  <Typography variant='body1'>{item.notes ?? '-'}</Typography>
                </Box>
              </Box>

              <Box sx={{ marginTop: 'auto' }}>
                {item?.status === ClaimStatuses.SUCCESS && (
                  <Box
                    sx={{
                      px: 2,
                      py: 1,
                      gap: 1,
                      display: 'flex',
                      alignItems: 'center',
                      paddingHorizontal: 16,
                      paddingVertical: 8,
                      borderRadius: 2,
                      backgroundColor: green[100],
                    }}
                  >
                    {/* @ts-ignore */}
                    <CheckIcon color={green[900]} height={18} width={18} sx={{ mt: 1 }} />
                    <Typography sx={{ fontWeight: '600', fontSize: 14, color: green[900], textAlign: 'center' }}>
                      Klaim ini berhasil di verifikasi
                    </Typography>
                  </Box>
                )}
                {item?.status === ClaimStatuses.PENDING && (
                  <Box
                    sx={{
                      px: 2,
                      py: 1,
                      gap: 1,
                      display: 'flex',
                      paddingHorizontal: 16,
                      paddingVertical: 8,
                      borderRadius: 1,
                      backgroundColor: amber[100],
                    }}
                  >
                    {/* @ts-ignore */}
                    <WarningIcon color={amber[900]} height={18} width={18} sx={{ mt: 1 }} />
                    <Box>
                      <Typography sx={{ fontWeight: '500', fontSize: 14, color: amber[900] }}>Klaim pending!</Typography>
                      <Typography sx={{ fontWeight: '500', fontSize: 12, color: amber[900] }}>
                        Hal ini disebabkan karena data yang di submit tidak sesuai atau belum di sinkronasi dengan player listing.
                      </Typography>
                    </Box>
                  </Box>
                )}
                {item?.status === ClaimStatuses.ERROR && (
                  <Box
                    sx={{
                      px: 2,
                      py: 1,
                      gap: 1,
                      display: 'flex',
                      alignItems: 'center',
                      paddingHorizontal: 16,
                      paddingVertical: 8,
                      borderRadius: 2,
                      backgroundColor: red[100],
                    }}
                  >
                    {/* @ts-ignore */}
                    <CloseFilledIcon color={red[900]} height={18} width={18} sx={{ mt: 1 }} />
                    <Typography sx={{ fontWeight: '600', fontSize: 14, color: red[900], textAlign: 'center' }}>Klaim error</Typography>
                  </Box>
                )}
              </Box>
              {item?.is_auto_claim && (
                <Box sx={{ display: 'flex', flexDirection: 'row', gap: 1, alignItems: 'center', mt: 1 }}>
                  <Box
                    sx={{
                      display: 'inline-flex',
                      backgroundColor: theme.palette.success.main,
                      flexDirection: 'row',
                      gap: 1,
                      alignItems: 'center',
                      mt: 1,
                      borderRadius: 4,
                      px: 0.8,
                      py: 0.2,
                    }}
                  >
                    <Box component={CheckIcon} sx={{ height: 14, width: 14, color: '#fff' }} />
                    <Typography sx={{ fontSize: 12, color: '#fff' }}>Auto Klaim</Typography>
                  </Box>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        {/* {isLoading && <AbsoluteFillLoading />} */}
      </Dialog>
    </Fragment>
  )
}

export default memo(DialogDetailClaim)
