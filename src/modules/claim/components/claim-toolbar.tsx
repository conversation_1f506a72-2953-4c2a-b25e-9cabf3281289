import { FC, useCallback, useEffect, useState } from 'react'

import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import IconButton from '@mui/material/IconButton'
import TextField from '@mui/material/TextField'
// icons
import SyncIcon from '@/assets/icons/bx--sync.svg?react'
// import ExportIcon from '@/assets/icons/lets-icons--export.svg?react'
import SearchIcon from '@/assets/icons/weui--search-filled.svg?react'
import { useClaim } from '@/modules/claim/hooks'
import { useAppDispatch } from '@/plugins/redux'

// components
import useDebounce from '@/modules/app/hooks/useDebounce.hook'
import { IMasterContactQueryParams } from '@/modules/master-contact/api/master-contact.api'
import { FormControl, InputAdornment, InputLabel, MenuItem, Select } from '@mui/material'

import ClearIcon from '@/assets/icons/grommet-icons--form-close.svg?react'
import { ClaimTypes } from '../constants'
import { useUser } from '@/modules/user/hooks'
import { SelectInputProps } from 'node_modules/@mui/material/esm/Select/SelectInput'

interface Props {
  type: ClaimTypes
}

const MasterContactToolbar: FC<Props> = ({ type }) => {
  const dispatch = useAppDispatch()
  const [selectedTelemarketingId, setSelectedTelemarketingId] = useState<number | null>(null)

  const { claim_fetchListClaimRegistration, claim_fetchListClaimFirstDeposit, claim_registerQueryParams, claim_firstDepositQueryParams } =
    useClaim()

  const { user_listOfTelemarketing } = useUser()

  const refetchData = async (params?: Partial<IMasterContactQueryParams>) => {
    if (type === ClaimTypes.REGISTER) {
      dispatch(claim_fetchListClaimRegistration({ ...claim_registerQueryParams, ...params }))
    } else {
      dispatch(claim_fetchListClaimFirstDeposit({ ...claim_firstDepositQueryParams, ...params }))
    }
  }

  const [searchExpanded, setSearchExpanded] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // Function to toggle the expanded state of the search bar
  const handleToggleExpand = () => {
    setSearchExpanded(!searchExpanded)
    // Clear search term when collapsing the search bar
    if (searchExpanded) {
      setSearchTerm('')
    }
  }

  // Function to handle search input changes
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
  }

  // Function to handle search submission (e.g., when pressing Enter or clicking search icon)
  const handleSearchSubmit = () => {
    refetchData({ query: searchTerm })
  }

  // const handleActionSuccess = useCallback(() => {
  //   dispatch(claim_fetchListClaimRegistration(claim_registerQueryParams))
  // }, [claim_registerQueryParams])

  // Get the debounced value after 500ms
  const debouncedSearchTerm = useDebounce(searchTerm, 500)

  const onChangeTelemarketing: SelectInputProps<number>['onChange'] = event => {
    const value = event.target.value as number
    setSelectedTelemarketingId(value)
    if (value) {
      if (type === ClaimTypes.REGISTER) {
        dispatch(claim_fetchListClaimRegistration({ ...claim_registerQueryParams, telemarketing_id: value }))
      } else {
        dispatch(claim_fetchListClaimFirstDeposit({ ...claim_firstDepositQueryParams, telemarketing_id: value }))
      }
    }
  }

  const handleClearSearch = useCallback(() => {
    setSearchTerm('')
    refetchData({ query: '', page: 1 })
  }, [searchTerm])

  // Effect to "perform the search" when the debounced value changes
  useEffect(() => {
    if (debouncedSearchTerm) {
      console.log('debouncedSearchTerm', debouncedSearchTerm)
      refetchData({ query: debouncedSearchTerm, page: 1, perPage: claim_registerQueryParams.perPage }).finally(() => {
        setSearchTerm(debouncedSearchTerm)
      })
      // performApiCall(debouncedSearchTerm);
    }
  }, [debouncedSearchTerm])

  return (
    <Stack
      direction='row'
      justifyContent='space-between'
      alignItems='center'
      sx={{ py: 2, backgroundColor: 'background.paper', px: 2, borderRadius: 2, mb: 3 }}
    >
      <Stack direction='row' gap={2}>
        <Stack sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
          {/* <ButtonGroup aria-label='filter view group btn'>
            {ClaimTypesOptions.map(item => (
              <Button variant='text' startIcon={<Box component={TableIcon} sx={{ height: 16, width: 16 }} />}>
                {item.label}
              </Button>
            ))}
          </ButtonGroup> */}
        </Stack>
        <Stack direction='row' gap={1} alignItems='center'>
          {/* search */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {/* Button to toggle the search bar expansion */}
            <IconButton size='small' edge='start' onClick={handleToggleExpand} sx={{ mr: 2 }}>
              <Box component={SearchIcon} sx={{ height: 22, width: 22 }} />
            </IconButton>

            {/* Search Input Field */}
            <TextField
              size='small'
              variant='outlined'
              placeholder='Search...'
              value={searchTerm}
              onChange={handleSearchChange}
              onKeyPress={e => {
                if (e.key === 'Enter') {
                  handleSearchSubmit()
                }
              }}
              sx={{
                width: searchExpanded ? '200px' : '0px',
                opacity: searchExpanded ? 1 : 0,
                transition: 'width 0.3s ease-in-out, opacity 0.3s ease-in-out', // Smooth transition
                overflow: 'hidden', // Hide overflow content during transition
                borderRadius: '8px', // Rounded corners for text field
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  paddingRight: searchExpanded ? '0px' : '0px', // Adjust padding when collapsed
                },
                '& .MuiInputBase-input': {},
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#e0e0e0', // Light border color
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#bdbdbd', // Darker border on hover
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#1976d2', // Material-UI primary color on focus
                },
              }}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position='end'>
                      {searchTerm && (
                        <IconButton aria-label='clear text' onClick={handleClearSearch} edge='start'>
                          <Box component={ClearIcon} sx={{ width: 16, height: 16 }} />
                        </IconButton>
                      )}
                    </InputAdornment>
                  ),
                },
              }}
            />
          </Box>
          <IconButton size='small' edge='start' onClick={() => refetchData()} sx={{ mr: 2 }}>
            <Box component={SyncIcon} sx={{ height: 22, width: 22 }} />
          </IconButton>
        </Stack>
      </Stack>
      <Stack direction='row' gap={2}>
        <Box sx={{ minWidth: 140 }}>
          <FormControl fullWidth>
            <InputLabel id='filter-tele'>Telemarketing</InputLabel>
            <Select
              labelId='filter-tele'
              id='demo-simple-select'
              value={selectedTelemarketingId}
              label='Telemarketing'
              // @ts-ignore
              onChange={onChangeTelemarketing}
            >
              {/* @ts-ignore */}
              <MenuItem value={null}>None</MenuItem>
              {user_listOfTelemarketing?.map((item, index) => (
                <MenuItem key={String(index)} value={item.id}>
                  {item.name}{' '}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
        {/* <Button size='small' variant='text' startIcon={<Box component={ExportIcon} sx={{ height: 22, width: 22 }} />}>
          Export
        </Button> */}
      </Stack>
    </Stack>
  )
}

export default MasterContactToolbar
