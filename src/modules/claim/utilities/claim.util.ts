// import ClockIcon from '@/assets/icons/majesticons--clock.svg'
// import CheckedOutlineIcon from '@/assets/icons/lets-icons--check-fill.svg'
// import CLoseOutlinedIcon from '@/assets/icons/nrk--close-active.svg'
import BillIcon from '@/assets/icons/solar--bill-list-bold-duotone.svg'

import { ClaimStatuses, ClaimTypes } from '../constants'

const getStatusColor = (status: ClaimStatuses) => {
  switch (Number(status)) {
    case ClaimStatuses.PENDING:
      return '#ff791f'
    case ClaimStatuses.ERROR:
      return '#f30303ff'
    case ClaimStatuses.REVIEW:
      return '#075dfd'
    case ClaimStatuses.SUCCESS:
      return '#009f5f'
    default:
      return '#626262'
  }
}

const getStatusIcon = (status: ClaimStatuses) => {
  switch (status) {
    // case ClaimStatuses.PENDING:
    //   return ClockIcon
    // case ClaimStatuses.ERROR:
    //   return CLoseOutlinedIcon
    // case ClaimStatuses.SUCCESS:
    //   return CheckedOutlineIcon
    default:
      return BillIcon
  }
}

const getName = (status: ClaimStatuses) => {
  switch (Number(status)) {
    case ClaimStatuses.PENDING:
      return 'Pending'
    case ClaimStatuses.ERROR:
      return 'Error'
    case ClaimStatuses.REVIEW:
      return 'Need Review'
    case ClaimStatuses.SUCCESS:
      return 'Success'
    default:
      return 'Unknown'
  }
}

const getClaimTypeName = (type: ClaimTypes) => {
  switch (type) {
    case ClaimTypes.REGISTER:
      return 'Registration (RG)'
    case ClaimTypes.FIRST_DEPOSIT:
      return 'First Deposit (FD)'
    default:
      return 'Unknown'
  }
}

export const ClaimUtils = {
  getStatusColor,
  getStatusIcon,
  getName,
  getClaimTypeName,
}
