declare global {
  interface IClaim {
    id: number
    telemarketing_id: number
    relation_id: number | null
    type: string
    status: number
    date: string
    player_id: string
    amount: number
    actual_amount: number
    country_code: number
    phone_number: string
    masterContact: IMasterContact
    telemarketing: IUser
    notes: string | null
    is_auto_claim: boolean
    created_at: string
    updated_at: string
  }
}

export {}
