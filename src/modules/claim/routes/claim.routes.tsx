import { Outlet, RouteObject } from 'react-router-dom'

// screens
import ClaimFirstDepositScreen from '@/modules/claim/screens/claim-first-deposit.screen'
import ClaimRegisScreen from '../screens/claim-regis.screen'

export const claimRoutes = (): RouteObject => ({
  path: 'claim',
  element: <Outlet />,
  children: [
    {
      path: '',
      element: <ClaimFirstDepositScreen />,
    },
    {
      path: 'registration',
      element: <ClaimRegisScreen />,
    },
    {
      path: 'fd',
      element: <ClaimFirstDepositScreen />,
    },
    // {
    //   path: 'mic',
    //   element: <ClaimFirstDepositScreen />,
    // },
  ],
})
