import Box from '@mui/material/Box'
import { useCallback, useEffect, useState } from 'react'
import { useAppDispatch } from '@/plugins/redux'
import { useClaim } from '@/modules/claim/hooks'

// icons
import BookBookmarkIcon from '@/assets/icons/solar--book-bookmark-bold-duotone.svg?react'
import ClaimToolbar from '@/modules/claim/components/claim-toolbar'
import { IClaimQueryParams } from '@/modules/claim/api/claim.api'
import PageHeader from '@/modules/app/components/page-header'
import { ClaimTypes } from '../constants'
import { CircularProgress, Grid, TablePagination } from '@mui/material'
import ClaimCardItem from '../components/claim-card-item'
import { ROWS_PER_PAGE_OPTIONS } from '@/modules/app/constants'
import DialogDetailClaim from '../components/dialog-detail-claim'
import { useUser } from '@/modules/user/hooks'

const ClaimRegistrationScreen = () => {
  const dispatch = useAppDispatch()

  const [selectedItem, setSelectedItem] = useState<IClaim | null>(null)
  const {
    claim_fetchListClaimRegistration,
    claim_registerQueryParams,
    claim_regisPagination,
    claim_regisListData: data,
    claim_regisLoading: loading,
  } = useClaim()
  const { user_fetchAllUserTelemarketing } = useUser()
  const refetchData = useCallback(
    (params: Partial<IClaimQueryParams>) => {
      const _params = { ...claim_registerQueryParams, ...params }
      dispatch(claim_fetchListClaimRegistration(_params))
    },
    [claim_registerQueryParams]
  )

  const onClickItem = useCallback(
    (claim: IClaim) => {
      if (claim) {
        setSelectedItem(claim)
      }
    },
    [selectedItem]
  )

  const onCloseModalDetail = useCallback(() => {
    setSelectedItem(null)
  }, [selectedItem])

  const handleChangePage = (_: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    refetchData({ page: newPage + 1 })
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    refetchData({ page: 1, perPage: parseInt(event.target.value, 10) })
  }

  const onApproveSuccess = useCallback(() => {
    refetchData({ ...claim_registerQueryParams })
  }, [])

  useEffect(() => {
    refetchData({ ...claim_registerQueryParams })
    dispatch(user_fetchAllUserTelemarketing())
  }, [])

  return (
    <Box>
      <PageHeader title='Claim Registration (RG)' subtitle='Manage data claim registration' icon={BookBookmarkIcon} />
      <Box sx={{ px: 3, pt: 5 }}>
        <ClaimToolbar type={ClaimTypes.REGISTER} />

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={2}>
            {data.map((item, index) => (
              <Grid key={String(index)} size={{ xs: 12, md: 3, lg: 4 }}>
                <ClaimCardItem index={index} item={item} onClickItem={onClickItem} />
              </Grid>
            ))}
            <TablePagination
              component='div'
              count={claim_regisPagination.total}
              page={claim_regisPagination.current_page - 1}
              onPageChange={handleChangePage}
              rowsPerPage={claim_regisPagination.per_page}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage='Data per page'
              rowsPerPageOptions={[...ROWS_PER_PAGE_OPTIONS]}
              sx={{ width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
            />
            <DialogDetailClaim
              open={Boolean(selectedItem)}
              item={selectedItem as IClaim}
              onClose={onCloseModalDetail}
              onApproveSuccess={onApproveSuccess}
            />
          </Grid>
        )}
      </Box>
    </Box>
  )
}

export default ClaimRegistrationScreen
