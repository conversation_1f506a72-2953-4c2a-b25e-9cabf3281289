import Box from '@mui/material/Box'
import { useCallback, useEffect, useState } from 'react'
import { useAppDispatch } from '@/plugins/redux'
import { useClaim } from '@/modules/claim/hooks'

// icons
import BookBookmarkIcon from '@/assets/icons/solar--book-bookmark-bold-duotone.svg?react'
import ClaimToolbar from '@/modules/claim/components/claim-toolbar'
import { IClaimQueryParams } from '@/modules/claim/api/claim.api'
import PageHeader from '@/modules/app/components/page-header'
import { ClaimTypes } from '../constants'
import CircularProgress from '@mui/material/CircularProgress'
import Grid from '@mui/material/Grid'
import TablePagination from '@mui/material/TablePagination'
import ClaimCardItem from '../components/claim-card-item'
import { ROWS_PER_PAGE_OPTIONS } from '@/modules/app/constants'
import DialogDetailClaim from '../components/dialog-detail-claim'

const ClaimFirstDepositScreen = () => {
  const dispatch = useAppDispatch()

  const [selectedItem, setSelectedItem] = useState<IClaim | null>(null)
  const {
    claim_fetchListClaimFirstDeposit,
    claim_firstDepositQueryParams,
    claim_dfPagination,
    claim_dfListData: data,
    claim_dfLoading: loading,
  } = useClaim()

  const refetchData = useCallback(
    (params: Partial<IClaimQueryParams>) => {
      const _params = { ...claim_firstDepositQueryParams, ...params }
      dispatch(claim_fetchListClaimFirstDeposit(_params))
    },
    [claim_firstDepositQueryParams]
  )

  const onClickItem = useCallback(
    (claim: IClaim) => {
      if (claim) {
        setSelectedItem(claim)
      }
    },
    [selectedItem]
  )

  const onCloseModalDetail = useCallback(() => {
    setSelectedItem(null)
  }, [selectedItem])

  const handleChangePage = (_: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    refetchData({ page: newPage + 1 })
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    refetchData({ page: 1, perPage: parseInt(event.target.value, 10) })
  }

  const onApproveSuccess = useCallback(() => {
    refetchData({ ...claim_firstDepositQueryParams })
  }, [])

  useEffect(() => {
    refetchData({ ...claim_firstDepositQueryParams })
  }, [])

  return (
    <Box>
      <PageHeader title='Claim First Deposit (FD)' subtitle='Manage data claim first deposit' icon={BookBookmarkIcon} />
      <Box sx={{ px: 3, pt: 5 }}>
        <ClaimToolbar type={ClaimTypes.FIRST_DEPOSIT} />

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={2}>
            {data.map((item, index) => (
              <Grid key={String(index)} size={{ xs: 12, md: 3, lg: 4 }}>
                <ClaimCardItem index={index} item={item} onClickItem={onClickItem} />
              </Grid>
            ))}
            <TablePagination
              component='div'
              count={claim_dfPagination.total}
              page={claim_dfPagination.current_page - 1}
              onPageChange={handleChangePage}
              rowsPerPage={claim_dfPagination.per_page}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage='Data per page'
              rowsPerPageOptions={[...ROWS_PER_PAGE_OPTIONS]}
              sx={{ width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
            />
            <DialogDetailClaim
              open={Boolean(selectedItem)}
              item={selectedItem as IClaim}
              onClose={onCloseModalDetail}
              onApproveSuccess={onApproveSuccess}
            />
          </Grid>
        )}
      </Box>
    </Box>
  )
}

export default ClaimFirstDepositScreen
