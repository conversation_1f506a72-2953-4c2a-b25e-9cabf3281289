// assets
import BookBookmarkIcon from '@/assets/icons/solar--book-bookmark-bold-duotone.svg?react'
import LayersIcon from '@/assets/icons/solar--layers-bold-duotone.svg?react'
import CpuBoltIcon from '@/assets/icons/solar--cpu-bolt-bold-duotone.svg?react'
import DollarIcon from '@/assets/icons/solar--chat-round-money-bold-duotone.svg?react'
import BillCheckIcon from '@/assets/icons/solar--bill-check-bold-duotone.svg?react'
// import SolarBoltIcon from '@/assets/icons/solar--bolt-bold-duotone.svg?react'
import PeopleIcon from '@/assets/icons/fluent--people-community-28-filled.svg?react'
import GamepadIcon from '@/assets/icons/solar--gamepad-charge-bold-duotone.svg?react'

export const AppMenus: IAppMenu[] = [
  {
    header: 'Report',
    items: [
      {
        label: 'Dashboard',
        icon: LayersIcon,
        path: '/app',
      },
      {
        label: 'Database Contact',
        icon: BookBookmarkIcon,
        path: '/db',
      },
      {
        label: 'Claim Regist',
        icon: Bill<PERSON><PERSON>ckIcon,
        path: '/claim/registration',
      },
      {
        label: 'Claim FD',
        icon: DollarIcon,
        path: '/claim/fd',
      },
    ],
  },

  {
    header: 'Player',
    items: [
      // {
      //   label: 'CRM Dashboard',
      //   icon: LayersIcon,
      //   path: '/crm',
      // },
      {
        label: 'Player Listing',
        icon: GamepadIcon,
        path: '/player-listing',
      },
    ],
  },

  {
    header: 'Configurations',
    items: [
      {
        label: 'Configurations',
        icon: CpuBoltIcon,
        path: '/configurations',
      },
    ],
  },

  {
    header: 'Internal',
    items: [
      {
        label: 'Employee',
        icon: PeopleIcon,
        path: '/user',
      },
    ],
  },
]
