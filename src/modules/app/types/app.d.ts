import { Dispatch, SetStateAction } from 'react'

import rootReducer from './plugins/redux/root-reducers'

declare global {
  interface IBaseApiResponse<T> {
    data: T
    message: string
  }

  interface IPaginateMeta {
    current_page: number
    from: number
    per_page: number
    to: number
    total: number
  }

  interface IPaginateResponse<T> {
    data: T[]
    meta: IPaginateMeta
  }

  interface IApiResponseError {
    message: string
  }

  interface IApiUnprocessableEntity extends IApiResponseError {
    errors?: {
      [key: string]: string[]
    }
  }

  interface IAppMenu {
    header: string
    items: {
      label: string
      icon: ReactElement
      path: string
    }[]
  }

  interface IBasePaginateRequest {
    paginate: number
    perPage: number
    page: number
  }
}

export {}
