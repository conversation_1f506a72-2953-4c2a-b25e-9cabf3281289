import { AssetsAvatars } from '@/assets/avatars'
import { AVATAR_LIST } from '../constants'

function getFirstLetters(inputString: string): string {
  // Trim leading/trailing whitespace and split the string by one or more spaces
  const words = inputString.trim().split(/\s+/)
  let initials = ''

  // Iterate over each word and append its first character to initials
  for (const word of words) {
    if (word.length > 0) {
      // Ensure the word is not empty (e.g., from multiple spaces)
      initials += word[0].toUpperCase() // Convert to uppercase as per "JD" example
    }
  }
  return initials?.length > 2 ? initials?.slice(0, 2) : initials
}

/**
 * Mengonversi ukuran dalam bytes ke KB atau MB tergantung ukurannya.
 *
 * @param bytes Ukuran file dalam bytes.
 * @returns String yang merepresentasikan ukuran dalam KB atau MB dengan satuan.
 */
function convertBytes(bytes: number): string {
  const ONE_MB_IN_BYTES = 1000 * 1000 // 1 MB = 1000 KB * 1000 bytes/KB

  if (bytes >= ONE_MB_IN_BYTES) {
    // Jika ukuran lebih dari atau sama dengan 1 MB, konversi ke MB
    const megabytes = bytes / ONE_MB_IN_BYTES
    return `${megabytes.toFixed(2)} MB` // Menggunakan toFixed(2) untuk 2 angka di belakang koma
  } else {
    // Jika ukuran kurang dari 1 MB, konversi ke KB
    const kilobytes = bytes / 1000 // 1 KB = 1000 bytes
    return `${kilobytes.toFixed(2)} KB` // Menggunakan toFixed(2) untuk 2 angka di belakang koma
  }
}

function formatToRupiah(value: number) {
  return (
    'IDR ' +
    Number(value).toLocaleString('id-ID', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  )
}

/**
 * Checks if an object is not empty.
 * @param {object} obj The object to check.
 * @returns {boolean} True if the object has one or more keys, false otherwise.
 */
function isObjectNotEmpty(obj: {}): boolean {
  // Use Object.keys() to get an array of the object's keys.
  // Then, check if the length of that array is greater than 0.
  return Object.keys(obj).length > 0
}

const getAvatarUser = (avatarId: string | null) => {
  return AVATAR_LIST.find(x => x.id === avatarId)?.img || AssetsAvatars.avatar11
}

export const AppUtils = {
  getFirstLetters,
  convertBytes,
  formatToRupiah,
  isObjectNotEmpty,
  getAvatarUser,
}
