// redux toolkit
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

export interface IAppSlice {
  app_isOffline: boolean
  app_openSidebar: boolean
  app_isDarkMode: boolean
}

// initial state
export const app_initialState: IAppSlice = {
  app_isOffline: false,
  app_openSidebar: true,
  app_isDarkMode: false,
}

export const appSlice = createSlice({
  name: 'app',
  initialState: app_initialState,
  reducers: {
    app_setIsOffline(state, action: PayloadAction<boolean>) {
      state.app_isOffline = action.payload
    },
    app_setOpenSidebar(state, action: PayloadAction<boolean>) {
      state.app_openSidebar = action.payload
    },
    app_setIsDarkMode(state, action: PayloadAction<boolean>) {
      state.app_isDarkMode = action.payload
    },
    app_reset: () => app_initialState,
  },
})

export const app_reducerActions = appSlice.actions

export const app_selector = (state: RootState): IAppSlice => state.app
