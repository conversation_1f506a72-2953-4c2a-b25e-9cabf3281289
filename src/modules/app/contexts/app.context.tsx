import { createContext, FC, ReactNode, useState } from 'react'

// product context
export const AppContext = createContext<IAppContext>({} as IAppContext)

interface Props {
  children: ReactNode
}

const AppContextProvider: FC<Props> = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false)

  return (
    <AppContext.Provider value={{ isDarkMode, setIsDarkMode }}>
      {children}
    </AppContext.Provider>
  )
}

export default AppContextProvider
