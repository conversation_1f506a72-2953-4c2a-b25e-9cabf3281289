import { FC, memo } from 'react'
import { Box, Typography } from '@mui/material'

interface Props {
  color: string
  label: string
}

const BaseLabelCard: FC<Props> = ({ color, label }) => {
  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        right: 0,
        px: 1.2,
        py: 0.4,
        pr: 0.7,
        borderBottomLeftRadius: '12px',
        backgroundColor: color,
      }}
    >
      <Typography style={{ color: '#fff', fontSize: 13, fontWeight: '600' }}>{label}</Typography>
    </Box>
  )
}

BaseLabelCard.displayName = 'BaseLabelCard'

export default memo(BaseLabelCard)
