import { FC, JSX, useCallback } from 'react'

// components
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'

// hooks
import { useApp } from '../hooks'

// constants
// import { PREFERRED_MODE_KEY } from '@/modules/app/constants'

import { motion, AnimatePresence } from 'framer-motion'
import { useAppDispatch } from '@/plugins/redux'

// SunIcon Component
const SunIcon = (): JSX.Element => (
  <motion.svg
    key='sun'
    xmlns='http://www.w3.org/2000/svg'
    viewBox='0 0 24 24'
    fill='none'
    stroke='currentColor'
    strokeWidth='2'
    strokeLinecap='round'
    strokeLinejoin='round'
    className='w-6 h-6 text-yellow-500'
    initial={{ scale: 0.5, opacity: 0, rotate: -90 }}
    animate={{ scale: 1, opacity: 1, rotate: 0 }}
    exit={{ scale: 0.5, opacity: 0, rotate: 90 }}
    transition={{ duration: 0.3, ease: 'easeInOut' }}
  >
    <circle cx='12' cy='12' r='5'></circle>
    <line x1='12' y1='1' x2='12' y2='3'></line>
    <line x1='12' y1='21' x2='12' y2='23'></line>
    <line x1='4.22' y1='4.22' x2='5.64' y2='5.64'></line>
    <line x1='18.36' y1='18.36' x2='19.78' y2='19.78'></line>
    <line x1='1' y1='12' x2='3' y2='12'></line>
    <line x1='21' y1='12' x2='23' y2='12'></line>
    <line x1='4.22' y1='19.78' x2='5.64' y2='18.36'></line>
    <line x1='18.36' y1='5.64' x2='19.78' y2='4.22'></line>
  </motion.svg>
)

// MoonIcon Component
const MoonIcon = (): JSX.Element => (
  <motion.svg
    key='moon'
    xmlns='http://www.w3.org/2000/svg'
    viewBox='0 0 24 24'
    fill='none'
    stroke='currentColor'
    strokeWidth='2'
    strokeLinecap='round'
    strokeLinejoin='round'
    className='w-6 h-6 text-slate-400'
    initial={{ scale: 0.5, opacity: 0, rotate: 90 }}
    animate={{ scale: 1, opacity: 1, rotate: 0 }}
    exit={{ scale: 0.5, opacity: 0, rotate: -90 }}
    transition={{ duration: 0.3, ease: 'easeInOut' }}
  >
    <path d='M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z'></path>
  </motion.svg>
)

// const saveCookies = (mode: string): void => {
//   // Cookie.set('preferred_color_mode', mode)
//   window.localStorage.setItem(PREFERRED_MODE_KEY, mode)
// }

const SwitchDarkMode: FC = () => {
  const { app_isDarkMode, app_setIsDarkMode } = useApp()

  const dispatch = useAppDispatch()

  const toggleDarkMode = useCallback(() => {
    // saveCookies(app_isDarkMode ? 'light' : 'dark')
    dispatch(app_setIsDarkMode(!app_isDarkMode))
  }, [app_isDarkMode, app_setIsDarkMode])

  return (
    <Box
      sx={{
        '& svg': { width: 20, height: 'auto' },
      }}
    >
      <Button size='medium' onClick={toggleDarkMode}>
        <AnimatePresence mode='wait' initial={false}>
          {app_isDarkMode ? <MoonIcon /> : <SunIcon />}
        </AnimatePresence>
      </Button>
    </Box>
  )
}

export default SwitchDarkMode
