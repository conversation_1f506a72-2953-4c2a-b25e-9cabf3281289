import { useState, ChangeEvent, DragEvent, Fragment, MouseEvent, useRef, FC, memo, useCallback } from 'react'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import Paper from '@mui/material/Paper'
import IconButton from '@mui/material/IconButton'

import CSVIcon from '@/assets/icons/csv.png'
import { AppUtils } from '../utils/app.util'

// icons
import TrashIcon from '@/assets/icons/solar--trash-bin-minimalistic-bold-duotone.svg?react'
import FileIcon from '@/assets/icons/solar--file-text-bold-duotone.svg?react'

const maxSizeInMB = 100
const acceptedFormats = ['.csv']

interface Props {
  isLoading?: boolean
  selectedFile: File
  onSelectFile: (file: File) => void
  onRemoveFile: () => void
}

const DraggableFileUploader: FC<Props> = ({ selectedFile, onSelectFile, onRemoveFile }) => {
  const [isDragOver, setIsDragOver] = useState(false)
  const [error, _] = useState<string>('')

  // input ref
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragOver(false)
    const files = e.dataTransfer.files
    if (files.length > 0) {
      onSelectFile(files[0])
    }
  }

  const onChangeInputFile = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      onSelectFile(files[0])
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const onClickBrowse = (e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation()
    fileInputRef?.current?.click?.()
  }

  const renderFileInfo = useCallback(() => {
    if (!selectedFile) {
      return null
    }

    return (
      <Box
        sx={{
          mt: 2,
          width: {
            xs: '100%',
            md: 320,
          },
          mx: 'auto',
          position: 'relative',
          borderRadius: 4,
          boxShadow: 1,
          px: 2,
          py: 1,
          border: theme => `1px solid ${theme.palette.divider}`,
        }}
      >
        <Stack>
          <Box component={FileIcon} sx={{ height: 18, width: 18, color: 'primary.main', mb: 0.5 }} />
          <Box>
            <Typography>{selectedFile?.name}</Typography>
            <Typography sx={{ color: 'text.secondary' }}>{AppUtils.convertBytes(selectedFile?.size)}</Typography>
          </Box>
        </Stack>
        <IconButton
          size='small'
          color='error'
          onClick={onRemoveFile}
          sx={{
            position: 'absolute',
            top: -3,
            right: -5,
            backgroundColor: 'error.main',
            '&:hover': {
              backgroundColor: 'error.dark',
            },
          }}
        >
          <Box component={TrashIcon} sx={{ height: 20, width: 20, color: 'common.white' }} />
        </IconButton>
      </Box>
    )
  }, [selectedFile])

  return (
    <Fragment>
      <Paper
        elevation={0}
        sx={{
          width: 320,
          border: 2,
          backgroundColor: isDragOver ? 'primary.light' : 'grey.300',
          borderColor: isDragOver ? 'primary.main' : 'grey.400',
          borderStyle: 'dashed',
          borderRadius: 5,
          p: 4,
          textAlign: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          bgcolor: 'background.paper',
          '&:hover': {
            borderColor: 'primary.main',
            backgroundColor: 'primary.light',
          },
          ...(error && {
            color: 'red',
          }),
        }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={onClickBrowse}
      >
        <Box component='img' src={CSVIcon} sx={{ width: 92, height: 'auto' }} />
        <Typography variant='h6' gutterBottom sx={{ color: 'text.secondary', lineHeight: 1.45 }}>
          {isDragOver ? (
            'Drop image here'
          ) : (
            <Stack>
              <Typography variant='h6' gutterBottom sx={{ color: 'text.secondary', lineHeight: 1.45 }}>
                Click to upload
              </Typography>
              <Typography gutterBottom sx={{ color: 'text.secondary', lineHeight: 1.45 }}>
                or drag and drop here.
              </Typography>
            </Stack>
          )}
        </Typography>
        {/* File constraints info */}
        <Box sx={{ mt: 2, height: 42 }}>
          {error ? (
            <Typography variant='caption' color='error'>
              {error}
            </Typography>
          ) : (
            <Fragment>
              <Typography variant='caption' color='text.secondary'>
                Max. File size {maxSizeInMB}MB
              </Typography>
              <br />
              <Typography variant='caption' color='text.secondary'>
                Supported: .CSV
              </Typography>
            </Fragment>
          )}
        </Box>
      </Paper>

      {renderFileInfo()}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type='file'
        accept={acceptedFormats.join(',')}
        multiple={false}
        onChange={onChangeInputFile}
        style={{ display: 'none' }}
        max={1}
      />
    </Fragment>
  )
}

export default memo(DraggableFileUploader)
