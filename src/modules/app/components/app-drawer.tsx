import { FC, Fragment, useCallback, useState } from 'react'

import { styled } from '@mui/material/styles'
import { useTheme } from '@mui/material/styles'
import { Theme } from '@mui/material/styles'
import { CSSObject } from '@mui/material/styles'
import MuiDrawer from '@mui/material/Drawer'
import MuiAppBar from '@mui/material/AppBar'
import { AppBarProps as MuiAppBarProps } from '@mui/material/AppBar'
import Toolbar from '@mui/material/Toolbar'
import List from '@mui/material/List'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import Divider from '@mui/material/Divider'
import IconButton from '@mui/material/IconButton'
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import ListItem from '@mui/material/ListItem'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import Box from '@mui/material/Box'
import Avatar from '@mui/material/Avatar'
import NotificationsIcon from '@mui/icons-material/Notifications'
import SettingsIcon from '@mui/icons-material/Settings'
import SupportAgentIcon from '@mui/icons-material/SupportAgent'

import AppLogo from '@/assets/icons/solar--bill-list-bold-duotone.svg?react'
import MenuIcon from '@/assets/icons/ion--menu.svg?react'

// components
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'

// icons
import Logout from '@mui/icons-material/Logout'
import Settings from '@mui/icons-material/Settings'

import AvatarImg from '@/assets/avatars/avatar_8.jpg'

import { useApp } from '@/modules/app/hooks'

// assets
import { AppMenus } from '../constants/app-menu.constant'
import { useNavigate } from 'react-router-dom'
import SwitchDarkMode from './switch-darkmode'

import { useAppDispatch } from '@/plugins/redux'

// configs
import { appConfig } from '../configs'
import { useAuth } from '@/modules/auth/hooks'

const drawerWidth = appConfig.sidebarWidth

const openedMixin = (theme: Theme): CSSObject => ({
  width: drawerWidth,
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: 'hidden',
})

const closedMixin = (theme: Theme): CSSObject => ({
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: 'hidden',
  width: `calc(${theme.spacing(6)} + 1px)`,
  [theme.breakpoints.up('sm')]: {
    width: `calc(${theme.spacing(7)} + 1px)`,
  },
})

const DrawerHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'flex-end',
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
}))

interface AppBarProps extends MuiAppBarProps {
  open?: boolean
}

const Main = styled('main', { shouldForwardProp: prop => prop !== 'open' })<{
  open?: boolean
}>(({ theme }) => ({
  flexGrow: 1,
  padding: theme.spacing(0),
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: '48px',
  variants: [
    {
      props: ({ open }) => open,
      style: {
        transition: theme.transitions.create('margin', {
          easing: theme.transitions.easing.easeOut,
          duration: theme.transitions.duration.enteringScreen,
        }),
        marginLeft: `${drawerWidth}px`,
      },
    },
  ],
}))

const AppBar = styled(MuiAppBar, {
  shouldForwardProp: prop => prop !== 'open',
})<AppBarProps>(({ theme }) => ({
  zIndex: theme.zIndex.drawer + 1,
  color: theme.palette.text.primary,
  backgroundColor: theme.palette.background.paper,
  // borderBottom: `1px solid ${theme.palette.divider}`,
  transition: theme.transitions.create(['width', 'margin'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  variants: [
    {
      props: ({ open }) => open,
      style: {
        marginLeft: drawerWidth,
        width: `calc(100% - ${drawerWidth}px)`,
        transition: theme.transitions.create(['width', 'margin'], {
          easing: theme.transitions.easing.sharp,
          duration: theme.transitions.duration.enteringScreen,
        }),
      },
    },
  ],
}))

const Drawer = styled(MuiDrawer, {
  shouldForwardProp: prop => prop !== 'open',
})(({ theme }) => ({
  width: drawerWidth,
  flexShrink: 0,
  whiteSpace: 'nowrap',
  boxSizing: 'border-box',
  variants: [
    {
      props: ({ open }) => open,
      style: {
        ...openedMixin(theme),
        '& .MuiDrawer-paper': openedMixin(theme),
      },
    },
    {
      props: ({ open }) => !open,
      style: {
        ...closedMixin(theme),
        '& .MuiDrawer-paper': closedMixin(theme),
      },
    },
  ],
}))

interface Props {
  children: React.ReactNode
}

const AppDrawer: FC<Props> = ({ children }) => {
  const theme = useTheme()

  const navigate = useNavigate()

  const dispatch = useAppDispatch()

  const { auth_revokeToken } = useAuth()

  const { app_openSidebar, app_setOpenSidebar } = useApp()

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

  const openAppBarMenu = Boolean(anchorEl)

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleDrawerOpen = () => {
    dispatch(app_setOpenSidebar(true))
  }

  const handleDrawerClose = () => {
    dispatch(app_setOpenSidebar(false))
  }

  const onClickItem = (path: string) => {
    navigate(path)
  }

  const onClickLogout = useCallback(() => {
    dispatch(auth_revokeToken())
      .unwrap()
      .finally(() => {
        window.location.href = '/auth/signin'
      })
  }, [])

  return (
    <Fragment>
      <AppBar position='fixed' open={app_openSidebar} elevation={0}>
        <Toolbar>
          <IconButton
            color='inherit'
            aria-label='open drawer'
            onClick={handleDrawerOpen}
            edge='start'
            sx={[
              {
                marginRight: 5,
              },
              app_openSidebar && { display: 'none' },
            ]}
          >
            <Box sx={{ height: 24, width: 24 }} component={MenuIcon} />
          </IconButton>
          {app_openSidebar && (
            <IconButton edge='start' onClick={handleDrawerClose} sx={{ mr: 2 }}>
              {theme.direction === 'rtl' ? <ChevronRightIcon /> : <ChevronLeftIcon />}
            </IconButton>
          )}
          <Stack
            direction='row'
            sx={{
              width: '100%',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Box>{!app_openSidebar && <Box sx={{ height: 26, width: 26, color: 'primary.main' }} component={AppLogo} />}</Box>
            <Stack>
              {/* Right-aligned icons and avatar */}
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Stack direction='row' gap={0.8}>
                  {/* Settings Icon */}
                  <IconButton color='inherit' aria-label='settings'>
                    <SettingsIcon />
                  </IconButton>
 
                  {/* Customer Support Icon */}
                  <IconButton color='inherit' aria-label='customer support'>
                    <SupportAgentIcon />
                  </IconButton>

                  {/* Notification Icon */}
                  <IconButton color='inherit' aria-label='notifications'>
                    <NotificationsIcon />
                  </IconButton>
                </Stack>

                <Divider orientation='vertical' flexItem sx={{ height: '20px', alignSelf: 'center', mx: 1 }} />

                {/* User Avatar */}
                <IconButton color='inherit' aria-label='user profile' sx={{ p: 0, ml: 2 }} onClick={handleClick}>
                  <Avatar alt='User Avatar' src={AvatarImg} sx={{ width: 36, height: 36 }} />
                </IconButton>

                {/* popover menu */}
                <Menu
                  anchorEl={anchorEl}
                  id='account-menu'
                  open={openAppBarMenu}
                  onClose={handleClose}
                  onClick={handleClose}
                  slotProps={{
                    paper: {
                      elevation: 0,
                      sx: {
                        overflow: 'visible',
                        filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                        mt: 1.5,
                        '& .MuiAvatar-root': {
                          width: 32,
                          height: 32,
                          ml: -0.5,
                          mr: 1,
                        },
                        '&::before': {
                          content: '""',
                          display: 'block',
                          position: 'absolute',
                          top: 0,
                          right: 14,
                          width: 10,
                          height: 10,
                          bgcolor: 'background.paper',
                          transform: 'translateY(-50%) rotate(45deg)',
                          zIndex: 0,
                        },
                      },
                    },
                  }}
                  transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                  anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                >
                  <MenuItem onClick={handleClose}>
                    <ListItemIcon>
                      <Settings fontSize='small' />
                    </ListItemIcon>
                    Settings
                  </MenuItem>
                  <MenuItem onClick={onClickLogout}>
                    <ListItemIcon>
                      <Logout fontSize='small' />
                    </ListItemIcon>
                    Logout
                  </MenuItem>
                </Menu>
              </Box>
            </Stack>
          </Stack>
        </Toolbar>
      </AppBar>
      <Drawer variant='permanent' open={app_openSidebar}>
        <DrawerHeader></DrawerHeader>
        {/* <Divider /> */}

        {/* sidebar menus */}
        <Stack
          sx={{
            transition: theme.transitions.create('margin', {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
            ...(app_openSidebar && { marginTop: '-30px' }),
          }}
        >
          {app_openSidebar && (
            <Box sx={{ pl: 2, gap: 2, display: 'flex', flexDirection: 'row', alignItems: 'center', mb: 2 }}>
              <Box sx={{ height: 36, width: 36, color: 'primary.main' }} component={AppLogo} />
              <Typography sx={{ fontSize: 17, fontWeight: '700' }}>{appConfig.appTitle}</Typography>
            </Box>
          )}
          {AppMenus.map((item, index) => (
            <Box
              key={String(index)}
              sx={{
                mb: 2,
              }}
            >
              <List>
                <Box
                  sx={{
                    pl: 2,
                    ...(!app_openSidebar && { display: 'none' }),
                  }}
                >
                  <Typography sx={{ fontSize: 12, fontWeight: '400', color: 'text.secondary' }}>{item.header}</Typography>
                </Box>
                {item.items.map((child, childIndex) => (
                  <ListItem
                    disablePadding
                    key={String(childIndex)}
                    sx={{
                      display: 'block',
                      ...(location.pathname == child.path && {
                        backgroundColor: theme.palette.mode == 'dark' ? 'primary.dark' : 'primary.light',
                      }),
                    }}
                  >
                    <ListItemButton
                      onClick={() => onClickItem(child.path)}
                      sx={[{ minHeight: 42, px: 2.5 }, app_openSidebar ? { justifyContent: 'initial' } : { justifyContent: 'center' }]}
                    >
                      <ListItemIcon sx={[{ minWidth: 0, justifyContent: 'center' }, app_openSidebar ? { mr: 2 } : { mr: 'auto' }]}>
                        <Box
                          sx={{ height: 22, width: 22, ...(location.pathname == child.path && { color: 'primary.main' }) }}
                          component={child.icon}
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={child.label}
                        sx={{
                          opacity: 0,
                          '> .MuiTypography-root ': {
                            fontWeight: '400',
                          },
                          ...(app_openSidebar && { opacity: 1 }),
                          ...(location.pathname == child.path && { color: 'primary.main' }),
                        }}
                      />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
              <Divider />
            </Box>
          ))}
        </Stack>

        <Stack
          sx={{
            mt: 'auto',
            ml: '2px',
            mb: 2,
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            ...(!app_openSidebar && { alignItems: 'center' }),
          }}
        >
          <SwitchDarkMode />
        </Stack>
      </Drawer>
      <Main open={app_openSidebar}>
        <DrawerHeader />
        {children}
      </Main>
    </Fragment>
  )
}

export default AppDrawer
