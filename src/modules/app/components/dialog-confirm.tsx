import { FC } from 'react'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import DialogContentText from '@mui/material/DialogContentText'
import WarningIcon from '@/assets/icons/fluent-color--warning-32.svg?react'

interface Props {
  itemId?: number
  open: boolean
  onConfirm: (itemId?: number) => void
  onCancel: () => void
  title: string
  subtitle?: string
}

const DialogConfirm: FC<Props> = ({ itemId, open, onCancel, onConfirm, title, subtitle }) => {
  const handleClose = () => {
    onCancel()
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      sx={{
        '& .MuiPaper-root': { borderRadius: 4 },
        '& .MuiDialog-paper': {
          boxShadow: 0,
          pt: 3,
          pb: 0.3,
        },
      }}
    >
      <Box component={WarningIcon} sx={{ width: 64, height: 64, margin: '0 auto', mb: 1 }} />
      <DialogTitle sx={{ textAlign: 'center', px: 0, py: 1 }}>{title}</DialogTitle>
      <DialogContent sx={{ textAlign: 'center' }}>
        <DialogContentText sx={{ color: 'text.secondary', fontSize: '0.85rem' }}>{subtitle}</DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>No</Button>
        <Button onClick={() => onConfirm(itemId)} autoFocus>
          Yes
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default DialogConfirm
