import { useAuth } from '@/modules/auth/hooks'
import { useMember } from '@/modules/member/hooks'
import { persistor } from '@/plugins/redux/store'
import { Button } from '@mui/material'
import { useCallback } from 'react'
import { useApp } from '../hooks'
import { useMasterContact } from '@/modules/master-contact/hooks'
import { useAppDispatch } from '@/plugins/redux'

const ClearCacheButton = () => {
  const dispatch = useAppDispatch()
  const { auth_reset } = useAuth()
  const { member_reset } = useMember()
  const { app_reset } = useApp()
  const { masterContact_reset } = useMasterContact()

  const onClickClearCache = useCallback(() => {
    dispatch(auth_reset())
    dispatch(member_reset())
    dispatch(app_reset())
    dispatch(masterContact_reset())
    persistor.purge()
  }, [])

  return (
    <Button onClick={onClickClearCache} variant='contained' color='error' sx={{ borderRadius: 20 }}>
      Reset App State
    </Button>
  )
}

export default ClearCacheButton
