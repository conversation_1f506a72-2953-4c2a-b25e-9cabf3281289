// router

// components
import Box from '@mui/material/Box'
import AppDrawer from '@/modules/app/components/app-drawer'
import AppFooter from '@/modules/app/components/app-footer.tsx'

// router
import { Outlet } from 'react-router'

const AppLayout = () => {
  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '100vh',
        backgroundColor: 'background.default',
      }}
    >
      <AppDrawer>
        <Outlet />
      </AppDrawer>
      <AppFooter />
    </Box>
  )
}

export default AppLayout
