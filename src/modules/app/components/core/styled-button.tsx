import { Theme } from '@mui/material'
import Box from '@mui/material/Box'
import { ButtonProps } from '@mui/material/Button'
import { styled } from '@mui/material/styles'
import { FC, MouseEvent, ReactElement, ReactNode } from 'react'

import CircularProgress from '@mui/material/CircularProgress'
import { ThemeConfig } from '../../configs'

const selectorCircleSvgSpinner = "& [class$='-Spinner'] > svg > circle"

interface BaseButtonProps extends Pick<ButtonProps, 'type' | 'startIcon' | 'endIcon' | 'fullWidth' | 'sx' | 'type' | 'disabled'> {
  onClick?: (e?: MouseEvent<HTMLButtonElement> | undefined | null) => void | undefined | null
  variant?: 'contained' | 'outlined' | 'text'
  color?: 'default' | 'primary' | 'secondary' | 'dark' | 'light'
  size?: 'small' | 'medium' | 'large'
  disableHoverEffect?: boolean
  isLoading?: boolean
}
interface StyledButtonRootProps extends BaseButtonProps {
  theme?: Theme
}

const StyledButtonRoot = styled('button', {
  shouldForwardProp: prop =>
    prop !== 'variant' &&
    prop !== 'color' &&
    prop !== 'size' &&
    prop !== 'disableHoverEffect' &&
    prop !== 'fullWidth' &&
    prop !== 'isLoading' &&
    prop !== 'disbaled',
})<StyledButtonRootProps>(({ theme, color, variant, size, disableHoverEffect, fullWidth, disabled }) => ({
  fontFamily: ThemeConfig.typography.fontFamily,
  fontWeight: '400',
  cursor: 'pointer',
  minWidth: 40,
  lineHeight: 1.5,
  borderRadius: Number(theme.shape.borderRadius),

  display: 'inline-flex',
  alignItems: 'center',
  userSelect: 'none',
  transform: 'unset',
  position: 'relative',
  overflow: 'hidden',
  border: 'none',
  whiteSpace: 'nowrap',
  WebkitTapHighlightColor: 'transparent',
  verticalAlign: 'middle',
  outline: 'none !important',
  transition: theme.transitions.create(['transform']),

  // Loading spinner
  // sample spinner className .css-14rey5t-Spinner'

  // It's doesn't works.
  // "& [class$='-Spinner'] > svg > circle": {
  //   '& svg': {
  //     '& circle': {
  //       ...(variant === 'contained' &&
  //         color !== 'light' && {
  //           stroke: `${theme.palette.primary.contrastText}  !important`,
  //         }),
  //       ...(variant === 'outlined' &&
  //         color === 'primary' && {
  //           stroke: `{theme.palette.primary.main}  !important`,
  //         }),
  //       ...(variant === 'outlined' &&
  //         color === 'secondary' && {
  //           stroke: `{theme.palette.secondary.secondary}  !important`,
  //         }),
  //     },
  //   },
  // },

  // Full width button
  ...(fullWidth && {
    width: '100%',
    justifyContent: 'center',
  }),

  // hover
  '&:hover': {
    ...(!disableHoverEffect && {
      transform: 'scale(1.03)',
    }),
  },

  '& svg': {
    fontSize: 20,
  },

  // sizes and variants
  ...(size === 'small' &&
    variant === 'outlined' && {
      padding: '4px 10px',
    }),
  ...(size === 'medium' &&
    variant === 'outlined' && {
      padding: '6px 14px',
    }),
  ...(size === 'large' &&
    variant === 'outlined' && {
      padding: '10px 18px',
      fontSize: 15,
    }),

  ...(size === 'small' &&
    variant !== 'outlined' && {
      padding: '6px 12px',
    }),
  ...(size === 'medium' &&
    variant !== 'outlined' && {
      padding: '8px 16px',
    }),
  ...(size === 'large' &&
    variant !== 'outlined' && {
      padding: '12px 20px',
      fontSize: 15,
    }),

  // variants
  ...(variant !== 'contained' && {
    backgroundColor: 'transparent',
    boxShadow: 'none !important',
  }),

  // colors & variants
  ...(color === 'default' &&
    variant === 'contained' && {
      backgroundColor: theme.palette.text.primary,
      color: theme.palette.primary.contrastText,
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.primary.contrastText} !important`,
      },
    }),
  ...(color === 'primary' &&
    variant === 'contained' && {
      backgroundColor: theme.palette.primary.main,
      color: theme.palette.primary.contrastText,
      // boxShadow: '0 6px 22px 0 rgb(18 124 113 / 12%)',
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.primary.contrastText} !important`,
      },
    }),
  ...(color === 'secondary' &&
    variant === 'contained' && {
      backgroundColor: theme.palette.secondary.main,
      color: theme.palette.primary.contrastText,
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.secondary.contrastText} !important`,
      },
    }),
  ...(color === 'dark' &&
    variant === 'contained' && {
      backgroundColor: '#313d56',
      color: theme.palette.primary.contrastText,
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.primary.main} !important`,
      },
    }),
  ...(color === 'light' &&
    variant === 'contained' && {
      backgroundColor: theme.palette.primary.contrastText,
      color: theme.palette.text.primary,
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.primary.contrastText} !important`,
      },
    }),

  ...(color === 'primary' &&
    variant === 'outlined' && {
      border: `2px solid ${theme.palette.primary.main}`,
      color: theme.palette.primary.main,
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.primary.main} !important`,
      },
    }),
  ...(color === 'secondary' &&
    variant === 'outlined' && {
      border: `2px solid ${theme.palette.secondary.main}`,
      color: theme.palette.secondary.main,
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.secondary.main} !important`,
      },
    }),
  ...(color === 'dark' &&
    variant === 'outlined' && {
      border: `2px solid #313d56`,
      color: '#313d56',
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.text.primary} !important`,
      },
    }),
  ...(color === 'light' &&
    variant === 'outlined' && {
      border: `2px solid #fbfbfb`,
      color: `#fbfbfb`,
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.primary.contrastText} !important`,
      },
    }),

  ...(color === 'primary' &&
    variant === 'text' && {
      color: theme.palette.primary.main,
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.primary.main} !important`,
      },
    }),
  ...(color === 'secondary' &&
    variant === 'text' && {
      color: theme.palette.secondary.main,
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.secondary.main} !important`,
      },
    }),
  ...(color === 'dark' &&
    variant === 'text' && {
      color: '#313d56',
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.text.primary} !important`,
      },
    }),
  ...(color === 'light' &&
    variant === 'text' && {
      color: theme.palette.primary.contrastText,
      [selectorCircleSvgSpinner]: {
        stroke: `${theme.palette.primary.contrastText} !important`,
      },
    }),

  // Disabled button
  ...(disabled && {
    backgroundColor: '#e1dede !important',
    color: '#a9a7a7 !important',
  }),
}))

interface Props extends BaseButtonProps {
  children?: ReactNode
}

const StyledButton: FC<Props> = (props: Props) => {
  const { children, onClick, disableHoverEffect, startIcon, endIcon, isLoading, disabled, ...rest } = props

  const renderSpinner = (): ReactElement => (
    <Box component='span' sx={{ display: 'inherit', mr: 1, ml: -0.25 }}>
      <CircularProgress size={18} />
    </Box>
  )

  // Handle button click.
  const handleClick = (e: MouseEvent<HTMLButtonElement>): void | undefined | null => {
    if (!onClick) return
    else {
      if (isLoading) e.preventDefault()
      else onClick(e)
    }
  }

  return (
    <StyledButtonRoot
      // @ts-ignore
      onClick={!disabled ? handleClick : () => null}
      disableHoverEffect={disableHoverEffect}
      disabled={disabled}
      {...rest}
    >
      {startIcon && !isLoading && (
        <Box
          component='span'
          sx={{
            display: 'inherit',
            mr: !children ? 0 : 1,
            ml: !children ? 0 : -0.5,
          }}
        >
          {startIcon}
        </Box>
      )}
      {isLoading && renderSpinner()}
      <Box component='span'>{children}</Box>
      {endIcon && !isLoading && (
        <Box
          component='span'
          sx={{
            display: 'inherit',
            ml: !children ? 0 : 1,
            mr: !children ? 0 : -0.5,
          }}
        >
          {endIcon}
        </Box>
      )}
    </StyledButtonRoot>
  )
}

export default StyledButton
