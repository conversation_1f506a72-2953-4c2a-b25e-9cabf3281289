// src/components/BaseDatePicker.tsx

import React, { useEffect } from 'react'
import { useC<PERSON>roller, UseControllerProps } from 'react-hook-form'
import { DatePicker, DatePickerProps } from '@mui/x-date-pickers/DatePicker'
import { SxProps, TextFieldProps } from '@mui/material'
import { LocalizationProvider } from '@mui/x-date-pickers'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'

interface BaseDatePickerProps extends UseControllerProps {
  name: string
  label: string
  size: 'small' | 'medium'
  // @ts-ignore
  datePickerProps?: Omit<DatePickerProps<Date>, 'value' | 'onChange' | 'renderInput'>
  helperText?: string
  textFieldProps?: TextFieldProps
  sx?: SxProps
}

const BaseDatePicker: React.FC<BaseDatePickerProps> = ({
  name,
  label,
  size,
  datePickerProps,
  helperText,
  textFieldProps,
  sx,
  disabled = false,
  ...rest
}) => {
  const {
    field,
    fieldState: { error },
  } = useController({ name, ...rest })

  const [cleared, setCleared] = React.useState<boolean>(false)

  useEffect(() => {
    if (cleared) {
      const timeout = setTimeout(() => {
        setCleared(false)
      }, 1500)

      return () => clearTimeout(timeout)
    }
    return () => {}
  }, [cleared])

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DatePicker
        label={label}
        value={field.value}
        onChange={date => field.onChange(date)}
        // @ts-ignore
        onBlur={field.onBlur}
        disabled={disabled}
        sx={{ '& .MuiPickersInputBase-root': { borderRadius: '10px' }, ...sx }}
        slotProps={{
          textField: {
            disabled: disabled,
            fullWidth: true,
            error: !!error,
            helperText: error ? error.message : (helperText ?? null),
            size,
            ...textFieldProps,
          },
          field: { clearable: true, onClear: () => setCleared(true) },
        }}
        {...datePickerProps}
      />
    </LocalizationProvider>
  )
}

export default BaseDatePicker
