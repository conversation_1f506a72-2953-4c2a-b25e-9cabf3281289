import { FC } from 'react'
import { use<PERSON><PERSON><PERSON><PERSON>, UseControllerProps } from 'react-hook-form'
import { FormControl, InputLabel, Select, MenuItem, FormHelperText, SelectProps } from '@mui/material'

interface BaseSelectProps extends UseControllerProps {
  name: string
  size: 'small' | 'medium'
  label: string
  options: { value: string; label: string }[]
  selectProps?: SelectProps
}

const BaseSelect: FC<BaseSelectProps> = ({ name, size, label, options, selectProps, ...rest }) => {
  const {
    field,
    fieldState: { error },
  } = useController({ name, ...rest })

  return (
    <FormControl fullWidth error={!!error} sx={{ mb: 2 }} size={size}>
      <InputLabel id={`${name}-label`} size={size}>
        {label}
      </InputLabel>
      <Select size={size} labelId={`${name}-label`} id={name} label={label} {...field} {...selectProps}>
        {options.map(option => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
      {error && <FormHelperText>{error.message}</FormHelperText>}
    </FormControl>
  )
}

export default BaseSelect
