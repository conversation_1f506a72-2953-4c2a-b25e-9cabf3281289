import { FC } from 'react'
import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'

interface Props {
  title: string
  subtitle: string
  icon: React.ElementType
}

const PageHeader: FC<Props> = ({ title, subtitle, icon }) => {
  return (
    <Stack sx={{ width: '100%', backgroundColor: 'background.paper' }}>
      <Box
        sx={theme => ({
          ...theme.mixins.toolbar,
        })}
      ></Box>
      <Stack
        sx={() => ({
          mb: 3,
          position: 'fixed',
          zIndex: 1,
          px: 3,
          py: 1.6,
          borderBottom: theme => `1px solid ${theme.palette.divider}`,
          backgroundColor: 'background.paper',
          width: '100%',
        })}
      >
        <Stack sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 1, mb: 0.4 }}>
          <Stack sx={{}}>
            <Box component={icon} sx={{ width: 26, height: 26, color: 'primary.main' }} />
          </Stack>
          <Typography variant='h4'>{title}</Typography>
        </Stack>
        <Box>
          <Typography variant='subtitle1' color='text.secondary'>
            {subtitle}
          </Typography>
        </Box>
      </Stack>
    </Stack>
  )
}

export default PageHeader
