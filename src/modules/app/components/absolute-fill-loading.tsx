import Box from '@mui/material/Box'
import CircularProgress from '@mui/material/CircularProgress'
import { FC } from 'react'

interface Props {
  opacity?: number
}

const AbsoluteFillLoading: FC<Props> = ({ opacity = 0.85 }) => {
  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: theme => (theme.palette.mode === 'dark' ? `rgba(0, 0, 0, ${opacity})` : `rgba(255, 255, 255, ${opacity})`),
        zIndex: 9999,
      }}
    >
      <CircularProgress />
    </Box>
  )
}

export default AbsoluteFillLoading
