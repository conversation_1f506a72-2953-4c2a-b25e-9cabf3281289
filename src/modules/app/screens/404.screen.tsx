import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'

// assets
import FourOFourImg from '@/assets/images/error-404.png'
import BackIcon from '@/assets/icons/solar--arrow-left-linear.svg?react'
import { useNavigate } from 'react-router-dom'

const NotFoundPage = () => {
  const navigate = useNavigate()
  const onClick = () => {
    navigate('/')
    // window.location.href = '/'
  }
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        textAlign: 'center',
        p: 3,
      }}
    >
      <Box component='img' src={FourOFourImg} alt='404 Not Found' sx={{ width: 120, height: 'auto', mb: 4 }} />
      <Typography
        variant='h1'
        component='h2'
        sx={{
          fontWeight: 500,
          mb: 2,
        }}
      >
        Ooops!
      </Typography>
      <Typography variant='body1' sx={{ mb: 4, fontSize: 16, color: 'text.secondary' }}>
        We can't find the page you're looking for...
      </Typography>
      <Button
        variant='contained'
        size='large'
        onClick={onClick}
        sx={{ borderRadius: 20, px: 4 }}
        disableElevation
        startIcon={<Box component={BackIcon} sx={{ height: 22, width: 22 }} />}
      >
        Go Back
      </Button>
    </Box>
  )
}

export default NotFoundPage
