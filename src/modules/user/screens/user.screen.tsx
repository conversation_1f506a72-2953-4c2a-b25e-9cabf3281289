import Box from '@mui/material/Box'
import { useCallback, useEffect } from 'react'
import { useAppDispatch } from '@/plugins/redux'

// icons
import BookBookmarkIcon from '@/assets/icons/solar--book-bookmark-bold-duotone.svg?react'
import UserToolbar from '../components/user-toolbar'
import { IUserQueryParams } from '../api/user.api'

import UserTable from '@/modules/user/components/user-table'

import PageHeader from '@/modules/app/components/page-header'

// hooks
import { useUser } from '@/modules/user/hooks'
import { CircularProgress } from '@mui/material'

const UserScreen = () => {
  const dispatch = useAppDispatch()

  const { user_fetchList, user_listData, user_listLoading, user_paginate, user_queryParams } = useUser()

  const refetchData = useCallback(
    (params: Partial<IUserQueryParams>) => {
      dispatch(user_fetchList({ ...user_queryParams, ...params }))
    },
    [user_queryParams]
  )

  useEffect(() => {
    refetchData(user_queryParams)
  }, [])

  return (
    <Box>
      {/* header  */}
      <PageHeader title='User Management' subtitle='Manage app user' icon={BookBookmarkIcon} />
      <Box sx={{ px: 3, pt: 5 }}>
        <Box sx={{ backgroundColor: 'background.paper', px: 2, pb: 0.3, pt: 1.5, borderRadius: 2 }}>
          <UserToolbar />
          {user_listLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
              <CircularProgress />
            </Box>
          ) : (
            <UserTable rows={user_listData} paginate={user_paginate} fetchData={refetchData} />
          )}
        </Box>
      </Box>
    </Box>
  )
}

export default UserScreen
