import { FC, memo, useCallback, useState } from 'react'
import Box from '@mui/material/Box'
import Paper from '@mui/material/Paper'
import Button from '@mui/material/Button'
import ButtonGroup from '@mui/material/ButtonGroup'
import Typography from '@mui/material/Typography'
import { DataGrid, GridColDef } from '@mui/x-data-grid'
import ArrowUpIcon from '@/assets/icons/mdi--arrow-up.svg?react'

import dayjs from 'dayjs'

// toast
import toast from 'react-hot-toast'

// assets
import Avatar from '@mui/material/Avatar'
import IconButton from '@mui/material/IconButton'
import Stack from '@mui/material/Stack'
import TablePagination from '@mui/material/TablePagination'
import { ROWS_PER_PAGE_OPTIONS } from '@/modules/app/constants'
import DialogConfirm from '@/modules/app/components/dialog-confirm'

// utils
import { AppUtils } from '@/modules/app/utils/app.util'
import { UserUtils } from '@/modules/user/utils/user.util'

// apis
import { IUserQueryParams, UserApi } from '../api/user.api'
import UserDrawerForm from './user-drawer-form'

interface Props {
  rows: IUser[]
  paginate: IPaginateMeta
  fetchData: (params: Partial<IUserQueryParams>) => void
}

const UserTable: FC<Props> = ({ rows, paginate, fetchData }) => {
  const [deleteId, setDeleteId] = useState<number | null>(null)
  const [editableUserData, setEditableUserData] = useState<IUser | null>(null)

  const handleChangePage = (_: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    fetchData({ page: newPage + 1 })
  }

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    fetchData({ page: 1, perPage: parseInt(event.target.value, 10) })
  }

  const handleClick = () => {}

  const onConfirmDelete = useCallback(async (itemId?: number) => {
    try {
      const response = await UserApi.delete(itemId as number)
      if (response) {
        handleCloseDialogConfirmDelete()
        toast.success('User deleted.', { duration: 3000, position: 'bottom-center' })
        fetchData({ page: paginate.current_page })
      }
    } catch (e) {
      console.log('e', e)
      toast.error('Failed to delete user.', { duration: 3000, position: 'bottom-center' })
    }
  }, [])

  const handleCloseDialogConfirmDelete = useCallback(() => {
    setDeleteId(null)
  }, [deleteId])

  const onUpdateSuccess = useCallback(() => {
    setEditableUserData(null)
    fetchData({})
  }, [fetchData, editableUserData])

  const columns: GridColDef<(typeof rows)[number]>[] = [
    {
      field: 'id',
      headerName: 'ID',
      sortable: true,
      width: 90,
      renderCell: params => (
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            cursor: 'pointer',
            '&:hover': {
              color: 'primary.main',
              textDecoration: 'underline',
            },
          }}
        >
          <Typography sx={{ fontWeight: 500, fontSize: '0.8rem', lineHeight: 1, color: 'inherit' }}>{params?.value}</Typography>
          <Box component={ArrowUpIcon} sx={{ height: 17, width: 17, color: 'inherit', ml: 0.4, transform: 'rotate(45deg)' }} />
        </Box>
      ),
    },
    {
      field: 'name',
      headerName: 'Name',
      width: 160,
      sortable: true,
      editable: false,
      renderCell: params => (
        <Stack
          sx={{
            width: '100%',
            flexDirection: 'row',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-start',
            gap: 1,
            height: '100%',
          }}
        >
          <IconButton color='inherit' aria-label='user profile' sx={{ p: 0 }} onClick={handleClick}>
            <Avatar
              src={params.row.avatar_id ? AppUtils.getAvatarUser(params.row.avatar_id) : (null as unknown as string)}
              alt='User Avatar'
              title={params.value}
              sx={{
                width: 30,
                height: 30,
                bgcolor: params.row?.avatar_text_color ?? 'primary.main',
                fontSize: '0.82rem',
                fontWeight: 500,
              }}
            >
              {AppUtils.getFirstLetters(params.value)}
            </Avatar>
          </IconButton>
          <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'center' }}>
            <Typography sx={{ fontWeight: '400', fontSize: '0.9rem', lineHeight: 1.7 }}>{params.value}</Typography>
            <Typography sx={{ fontWeight: '400', fontSize: '0.75rem', color: 'text.secondary', lineHeight: 1.2 }}>
              {params.row.referral_code}
            </Typography>
          </Box>
        </Stack>
      ),
    },
    {
      field: 'phone_number',
      sortable: true,
      headerName: 'Phone Number',
      description: 'This column has a value getter and is not sortable.',
      width: 160,
      valueGetter: (_, row) => `${row.country_code || ''}  ${row.phone_number || ''}`,
    },
    {
      field: 'email',
      headerName: 'Email',
      sortable: false,
      description: 'This column has a value getter and is not sortable.',
      width: 180,
      valueGetter: (_, row) => `${row?.email || '-'}`,
    },
    {
      field: 'referral_code',
      headerName: 'Referral Code',
      sortable: false,
      width: 180,
      renderCell: params => (
        <Box sx={{ height: '100%', flexDirection: 'row', display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography sx={{ fontSize: '0.8rem', lineHeight: 1, color: UserUtils.getRoleColor(params.value), fontWeight: '400' }}>
            {params.value}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'role',
      headerName: 'Role',
      sortable: true,
      width: 132,
      renderCell: params => (
        <Box sx={{ height: '100%', flexDirection: 'row', display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            component={UserUtils.getRoleIcon(params.value)}
            sx={{ height: 16, width: 16, color: UserUtils.getRoleColor(params.value) }}
          />
          <Typography sx={{ fontSize: '0.8rem', lineHeight: 1, color: UserUtils.getRoleColor(params.value), fontWeight: '400' }}>
            {UserUtils.getRoleName(params.value)}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      sortable: true,
      width: 132,
      renderCell: params => (
        <Box sx={{ height: '100%', flexDirection: 'row', display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            component={UserUtils.getStatusIcon(params.value)}
            sx={{ height: 16, width: 16, color: UserUtils.getStatusColor(params.value) }}
          />
          <Typography sx={{ fontSize: '0.8rem', lineHeight: 1, color: UserUtils.getStatusColor(params.value), fontWeight: '400' }}>
            {UserUtils.getStatusName(params.value)}
          </Typography>
        </Box>
      ),
    },
    {
      field: 'gender',
      headerName: 'Gender',
      sortable: true,
      description: 'This column has a value getter and is not sortable.',
      width: 100,
      valueGetter: (_, row) => `${row.gender || '-'}`,
    },
    {
      field: 'created_at',
      headerName: 'Join Date',
      width: 120,
      editable: false,
      align: 'left',
      renderCell: params => (
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'center' }}>
          <Typography sx={{ fontWeight: '400', fontSize: '0.8rem', lineHeight: 1.5 }}>
            {dayjs(params?.row?.created_at).format('DD MMM YYYY')}
          </Typography>
          <Typography sx={{ fontWeight: '400', fontSize: '0.7rem', color: 'text.secondary', lineHeight: 1.2 }}>
            {dayjs(params?.row?.created_at).format('HH:mm')} WIB
          </Typography>
        </Box>
      ),
    },
    {
      field: 'country_code',
      headerName: 'Actions',
      width: 140,
      sortable: false,
      editable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: params => (
        <ButtonGroup size='small' aria-label='filter view group btn'>
          <Button variant='outlined' size='small' color='info' onClick={() => setEditableUserData(params.row)}>
            Edit
          </Button>
          <Button variant='outlined' size='small' color='success'>
            View
          </Button>
        </ButtonGroup>
      ),
    },
  ]

  return (
    <Paper
      elevation={0}
      sx={{
        p: 0,
        width: '100%',
        '& .MuiDataGrid-columnHeadersContainer': {
          backgroundColor: 'background.paper',
        },
      }}
    >
      <DataGrid
        rowHeight={52}
        rows={rows}
        columns={columns}
        checkboxSelection
        sx={{
          backgroundColor: 'background.paper',
          borderColor: 'divider',
          '& .MuiDataGrid-cell': {
            fontSize: '0.9rem',
            fontWeight: '400',
          },
          '& .MuiDataGrid-columnHeaderTitle': {
            fontWeight: 500,
            fontSize: '0.9rem',
          },
        }}
        hideFooter
        disableRowSelectionOnClick
      />
      <TablePagination
        component='div'
        count={paginate.total}
        page={paginate.current_page - 1}
        onPageChange={handleChangePage}
        rowsPerPage={paginate.per_page}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={[...ROWS_PER_PAGE_OPTIONS]}
      />
      <DialogConfirm
        itemId={deleteId as number}
        open={Boolean(deleteId)}
        onCancel={handleCloseDialogConfirmDelete}
        onConfirm={onConfirmDelete}
        title='Are you sure?'
        subtitle="You won't be able to revert this!"
      />
      <UserDrawerForm
        open={Boolean(editableUserData?.id)}
        onClose={() => setEditableUserData(null)}
        onSaveSuccess={onUpdateSuccess}
        defaultValues={editableUserData as IUser}
      />
    </Paper>
  )
}

export default memo(UserTable)
