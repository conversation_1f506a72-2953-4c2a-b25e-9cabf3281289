import { FC, memo, useCallback, useEffect, useState } from 'react'
import {
  Drawer,
  Button,
  Box,
  TextField,
  Typography,
  IconButton,
  Avatar,
  InputAdornment,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormHelperText,
  OutlinedInputProps,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'

// api
import { IStoreUserRequest, UserApi } from '@/modules/user/api/user.api'
import { DatePicker, Select } from '@/modules/app/components/core'
import { RolesOptions, UserGenders, UserRoles, UserStatuses } from '@/modules/user/constants/user.constant'
import AvatarImg from '@/assets/avatars/avatar_7.jpg'

import dayjs from 'dayjs'
import { Swal2 } from '@/plugins/sweetalert2'
import AbsoluteFillLoading from '@/modules/app/components/absolute-fill-loading'

const schema = yup
  .object({
    id: yup.number().nullable(),
    name: yup.string().required('Name is required'),
    // username: yup
    //   .string()
    //   .min(3, 'username minimal 3 karakter.')
    //   .max(20, 'username maksimal 20 karakter.')
    //   .matches(/^[a-zA-Z0-9._]+$/, 'username hanya boleh mengandung huruf, angka, titik, dan underscore.')
    //   .trim('username tidak boleh mengandung spasi di awal atau akhir.'),
    email: yup.string().email('Invalid email format').required('Email is required'),
    phone_number: yup
      .string()
      .matches(/^(8)\d{8,16}$/, 'Nomor HP tidak valid')
      .nullable(),
    // password: yup.string().required('Password is required'),
    password: yup
      .string()
      // The conditional logic is here, using .when()
      .when('id', {
        is: (id: number | null) => id === null, // Checks if the bio has a value
        then: schema => schema.required('Password is required'), // If it has a value, it must not be an empty string
        otherwise: schema => schema.notRequired(), // If it's empty, it's not required
      }),
    role: yup.number().required('Role is required'),
    notes: yup.string(),
  })
  .required()

interface Props {
  open: boolean
  onClose: () => void
  onSaveSuccess: () => void
  defaultValues: IUser | null
}

const UserDrawerForm: FC<Props> = ({ open, onClose, onSaveSuccess, defaultValues }) => {
  const [isLoading, setIsLoading] = useState(false)
  const {
    handleSubmit,
    control,
    formState: { errors, dirtyFields },
    reset,
    setValue,
    watch,
  } = useForm<IStoreUserRequest>({
    // @ts-ignore
    resolver: yupResolver(schema),
    defaultValues: {
      id: null,
      name: '',
      referral_code: '',
      email: '',
      phone_number: null,
      role: null,
      join_date: new Date().toString(),
      notes: '',
      status: UserStatuses.ACTIVE,
    },
  })

  const roleOptions = Array.from([...RolesOptions].map(i => ({ value: i.value, label: i.label })))

  const onValidSubmit: SubmitHandler<IStoreUserRequest> = useCallback(async values => {
    try {
      setIsLoading(true)
      const joinDate = values.join_date ? dayjs(values.join_date).format('YYYY-MM-DD HH:mm:ss') : null
      if (values.id) {
        const response = await UserApi.updateUser({ ...values, join_date: joinDate })
        if (response.id) {
          onSaveSuccess()
          reset()
        }
      } else {
        const response = await UserApi.create({ ...values, join_date: joinDate })
        if (response.id) {
          onSaveSuccess()
          reset()
          onClose?.()
        }
      }
    } catch (e) {
      //  setDrawerIsOpen()
      setIsLoading(false)
    } finally {
      setIsLoading(false)
    }
  }, [])

  console.log(watch('join_date'))

  const onClickAvatar = () => {}

  const onChangePhoneNumber: OutlinedInputProps['onChange'] = event => {
    // 2. This function handles the change event from the input field.

    let value = event.target.value

    // 3. This is the core logic. We check if the input starts with '0'.
    if (value.startsWith('0')) {
      // If it does, we remove the first character using slice(1).
      // This creates a new string without the leading '0'.
      value = value.slice(1)
    }

    // 4. We update the state with the new, potentially modified value.
    // setPhoneNumber(value)
    setValue('phone_number', value)
  }

  useEffect(() => {
    if (defaultValues?.id) {
      setValue('id', defaultValues.id)
      setValue('name', defaultValues.name)
      setValue('email', defaultValues.email)
      setValue('referral_code', defaultValues.referral_code)
      setValue('phone_number', defaultValues.phone_number)
      setValue('role', defaultValues.role)
      setValue('join_date', defaultValues.join_date)
      setValue('notes', defaultValues.notes)
      setValue('gender', defaultValues.gender)
      setValue('status', defaultValues.status)
    } else {
      setTimeout(() => {
        setValue('role', UserRoles.TELEMARKETING)
      }, 500)
    }
  }, [defaultValues])

  const onCloseDrawer = useCallback(() => {
    if (Object.keys(dirtyFields).length > 0) {
      Swal2.fire({
        title: 'Konfirmasi',
        text: 'Apakah kamu yakin ingin keluar?',
        icon: 'warning',
        showCancelButton: true,
        cancelButtonText: 'Tidak',
        confirmButtonText: 'Ya',
      }).then(result => {
        if (result.isConfirmed) {
          reset()
          onClose?.()
        }
      })
    } else {
      reset()
      onClose?.()
    }
  }, [dirtyFields])

  return (
    <div>
      <Drawer
        anchor='right'
        open={open}
        onClose={onCloseDrawer}
        sx={{
          zIndex: 1300,
          mr: 4,
          '& .MuiPaper-root': {
            height: '100vh',
            backgroundColor: 'transparent !important',
            boxShadow: 0,
            backgroundImage: 'none',
            overflow: 'hidden',
          },
        }}
      >
        <Box
          sx={{
            my: 2,
            mr: 2,
            boxShadow: 0,
            borderRadius: 4,
            backgroundColor: 'background.paper',
            height: '100%',
            overflow: 'hidden',
            position: 'relative',
          }}
        >
          <Box
            sx={{
              width: 392,
              px: 4,
              pt: 2,
              display: 'flex',
              flexDirection: 'column',
              height: '100%',
              overflowY: 'scroll',
            }}
            role='presentation'
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 2 }}>
              <Typography variant='h6' component='h2'>
                Add New User
              </Typography>
              <IconButton onClick={onCloseDrawer}>
                <CloseIcon />
              </IconButton>
            </Box>
            {/* @ts-ignore */}
            <Box component='form' onSubmit={handleSubmit(onValidSubmit)} sx={{ height: '100%' }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Avatar onClick={onClickAvatar} alt='User Avatar' src={AvatarImg} sx={{ width: 72, height: 72 }} />
              </Box>
              <Select
                name='role'
                size='small'
                label='Role'
                placeholder='Select role'
                // @ts-ignore
                control={control}
                // @ts-ignore
                options={roleOptions}
                rules={{ required: 'This field is required' }} // React Hook Form's built-in validation
              />

              <DatePicker
                name='join_date'
                size='small'
                label='Select Join Date'
                // @ts-ignore
                control={control}
                sx={{ mb: 2 }}
                disabled={Boolean(defaultValues?.join_date)}
                // No need for rules={{ required: true }} if using yupResolver
              />

              <Controller
                name='name'
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    size='small'
                    label='Nama Lengkap'
                    variant='outlined'
                    fullWidth
                    error={!!errors.name}
                    helperText={errors.name ? errors.name.message : ''}
                    sx={{ mb: 2 }}
                  />
                )}
              />

              <Controller
                name='referral_code'
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    size='small'
                    label='Referall code'
                    variant='outlined'
                    fullWidth
                    error={!!errors.referral_code}
                    helperText={errors.referral_code ? errors.referral_code.message : 'Untuk telemarketing bidang ini wajib di isi'}
                    sx={{ mb: 2 }}
                  />
                )}
              />

              <Controller
                name='email'
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    size='small'
                    label='Email'
                    variant='outlined'
                    fullWidth
                    error={!!errors.email}
                    helperText={errors.email ? errors.email.message : ''}
                    sx={{ mb: 2 }}
                  />
                )}
              />

              <Controller
                name='phone_number'
                control={control}
                rules={{ required: 'Phone number is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    size='small'
                    label='Phone Number'
                    variant='outlined'
                    fullWidth
                    error={!!errors.phone_number}
                    helperText={errors.phone_number ? errors.phone_number.message : ''}
                    sx={{ mb: 2 }}
                    onChange={onChangePhoneNumber}
                    slotProps={{
                      input: {
                        startAdornment: <InputAdornment position='start'>+62</InputAdornment>,
                      },
                    }}
                  />
                )}
              />

              <Controller
                name='password'
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    size='small'
                    label='Password'
                    variant='outlined'
                    fullWidth
                    error={!!errors.password}
                    helperText={errors.password ? errors.password.message : 'Password untuk login'}
                    sx={{ mb: 2 }}
                  />
                )}
              />

              <FormControl component='fieldset' error={!!errors.gender} sx={{ mb: 2 }}>
                <FormLabel component='legend' sx={{ fontSize: '0.9rem', fontWeight: '400' }}>
                  Gender
                </FormLabel>
                <Controller
                  control={control}
                  name='gender'
                  render={({ field }) => (
                    <RadioGroup {...field} row>
                      <FormControlLabel value={UserGenders.FEMALE} control={<Radio />} label='Female' />
                      <FormControlLabel value={UserGenders.MALE} control={<Radio />} label='Male' />
                    </RadioGroup>
                  )}
                />
                {errors.gender && <FormHelperText>{errors.gender.message}</FormHelperText>}
              </FormControl>

              <Controller
                name='notes'
                control={control}
                render={({ field }) => (
                  <TextField
                    multiline
                    rows={3}
                    {...field}
                    size='small'
                    label='Notes'
                    variant='outlined'
                    fullWidth
                    error={!!errors.notes}
                    helperText={errors.notes ? errors.notes.message : 'Berikan catatan jika diperlukan'}
                    sx={{ mb: 2 }}
                  />
                )}
              />

              <FormControl component='fieldset' error={!!errors.gender}>
                <FormLabel component='legend' sx={{ fontSize: '0.9rem', fontWeight: '400' }}>
                  Status
                </FormLabel>
                <Controller
                  control={control}
                  name='status'
                  render={({ field }) => (
                    <RadioGroup {...field} row>
                      <FormControlLabel value={UserStatuses.ACTIVE} control={<Radio />} label='Active' />
                      <FormControlLabel value={UserStatuses.INACTIVE} control={<Radio />} label='Inactive' />
                    </RadioGroup>
                  )}
                />
                {errors.status && <FormHelperText>{errors.status.message}</FormHelperText>}
              </FormControl>

              <Button disableElevation type='submit' size='large' fullWidth variant='contained' color='primary' sx={{ mt: 2 }}>
                Save
              </Button>
              <Box sx={{ height: 40 }} />
            </Box>
            {isLoading && <AbsoluteFillLoading opacity={0.75} />}
          </Box>
        </Box>
      </Drawer>
    </div>
  )
}

export default memo(UserDrawerForm)
