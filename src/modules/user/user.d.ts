import { Dispatch, SetStateAction } from 'react'

declare global {
  interface IUser {
    id: number
    name: string
    referral_code: string
    phone_number: string
    country_code: string
    email: string
    email_verified_at: string | null
    photo_url: string | null
    avatar_text_color: string | null
    avatar_id: string | null
    gender: string | null
    status: number
    role: number
    join_date: string
    notes: string
    created_at: string
    updated_at: string
  }
}

export {}
