import { createAsyncThunk } from '@reduxjs/toolkit'
import { UserApi } from '../api/user.api'

export const user_fetchList = createAsyncThunk(
  '@user/fetchList',
  async (params: IBasePaginateRequest): Promise<IPaginateResponse<IUser>> => {
    return await UserApi.fetchList(params)
  }
)

export const user_fetchAllUserTelemarketing = createAsyncThunk('@user/fetchAllUserTelemarketing', async (): Promise<IUser[]> => {
  return await UserApi.fetchAllUserTelemarketing()
})
