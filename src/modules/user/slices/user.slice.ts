// redux toolkit
import { createSlice } from '@reduxjs/toolkit'

import { user_fetchList,user_fetchAllUserTelemarketing } from './user.thunk'
import { IUserQueryParams } from '../api/user.api'
import { ROWS_PER_PAGE_OPTIONS } from '@/modules/app/constants'

// type for our state
export type IUserSlice = {
  user_listData: IUser[]
  user_listLoading: boolean
  user_paginate: IPaginateMeta
  user_queryParams: IUserQueryParams
  user_listOfTelemarketing: IUser[]
}

// initial state
export const user_initialState: IUserSlice = {
  user_listData: [],
  user_listLoading: false,
  user_paginate: {
    current_page: 1,
    from: 0,
    per_page: 0,
    to: 0,
    total: 0,
  },
  user_queryParams: {
    paginate: 1,
    perPage: ROWS_PER_PAGE_OPTIONS[0],
    page: 1,
  },
  user_listOfTelemarketing: [],
}

export const userSlice = createSlice({
  name: 'user',
  initialState: user_initialState,
  reducers: {
    user_reset: () => user_initialState,
  },
  extraReducers: builder => {
    builder.addCase(user_fetchList.pending, (state, _) => {
      state.user_listLoading = true
    })
    builder.addCase(user_fetchList.rejected, (state, _) => {
      state.user_listLoading = false
    })
    builder.addCase(user_fetchList.fulfilled, (state, action) => {
      state.user_listLoading = false

      if (Array.isArray(action.payload.data)) {
        state.user_listData = action.payload.data
      }

      if (action.payload.meta) {
        state.user_paginate = action.payload.meta

        state.user_queryParams.paginate = 1
        state.user_queryParams.page = action.payload.meta.current_page
        state.user_queryParams.perPage = action.payload.meta.per_page
      }
    })

    builder.addCase(user_fetchAllUserTelemarketing.fulfilled, (state, action) => {
      state.user_listOfTelemarketing = action.payload
    })
  }, 
})

export const user_reducerActions = userSlice.actions

export const user_selector = (state: RootState): IUserSlice => state.user
