import axiosInstance from '@/plugins/axios/axios-instance'

export interface IUserQueryParams extends IBasePaginateRequest {
  name?: string | null
  username?: string | null
  email?: string | null
  phone_number?: string | null
  role?: IUser['role'] | null
  gender?: string | null
  join_date?: string | null
  notes?: string | null
  status?: IUser['status'] | null
}

export interface IStoreUserRequest {
  id: number | null
  name: string
  email: string
  referral_code: string
  password: string
  phone_number: string | null
  role: IUser['role'] | null
  gender: string | null
  join_date: string | null
  notes?: string | null
  status: IUser['status'] | null
}

export const UserApi = {
  fetchList: async (_params: IBasePaginateRequest): Promise<IPaginateResponse<IUser>> => {
    const params: IBasePaginateRequest = {
      // @ts-ignore
      paginate: 1,
      ..._params,
    }
    const response = await axiosInstance.get('/user', { params })
    return response?.data
  },

  create: async (body: IStoreUserRequest): Promise<IUser> => {
    const response = await axiosInstance.post('/user', body)
    return response?.data
  },

  updateUser: async (body: IStoreUserRequest): Promise<IUser> => {
    const response = await axiosInstance.put(`/user/${body.id}`, body)
    return response?.data
  },

  delete: async (userId: number): Promise<IBaseApiResponse<unknown>> => {
    const response = await axiosInstance.delete(`/user/${userId}`)
    return response.data
  },

  fetchAllUserTelemarketing: async (): Promise<IUser[]> => {
    const response = await axiosInstance.get(`/user/telemarketing`)
    return response.data
  },
}
