// @mui utils
import { createTheme as createMuiTheme } from '@mui/material/styles'
import { Theme } from '@mui/material/styles'

// theme configs
import { ThemeConfig } from './modules/app/configs'

export const createTheme = (isDark?: boolean): Theme => {
  const palette = isDark ? ThemeConfig.paletteDark : ThemeConfig.paletteLight
  return createMuiTheme({
    palette,
    typography: ThemeConfig.typography,
    breakpoints: ThemeConfig.breakpoints,
    shadows: ThemeConfig.shadows,
    components: ThemeConfig.components,
    zIndex: ThemeConfig.zIndex,
    mixins: ThemeConfig.mixins,
  })
}
