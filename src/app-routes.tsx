import { FC, Suspense, lazy } from 'react'

// router
import { useRoutes } from 'react-router'
import { Navigate, RouteObject } from 'react-router-dom'

// components
import AppLayout from './modules/app/components/app-layout'
import LoadingScreen from './modules/app/components/loading-screen'
import AuthenticationGuard from './modules/auth/components/auth-guard'

// routes
import { authRoutes } from '@/modules/auth/routes'
import { dashboardRoutes } from '@/modules/dashboard/routes'
import { masterContactRoutes } from './modules/master-contact/routes/master-contact.routes'
import { userRoutes } from './modules/user/routes/user.routes'
import { claimRoutes } from './modules/claim/routes/claim.routes'
import { memberRoutes } from './modules/member/routes/member.routes'
import { crmRoutes } from './modules/crm/routes/crm.routes'
import { configurationRoutes } from './modules/configuration/routes/configuration.routes'

// pages components
const PageNotFound = lazy(() => import('./modules/app/screens/404.screen'))

const rootRoutes = (isLoggedIn: boolean): Array<RouteObject> => [
  { ...authRoutes() },
  // app Routes,
  {
    path: '',
    element: (
      <AuthenticationGuard>
        <AppLayout />
      </AuthenticationGuard>
    ),
    children: [
      { ...dashboardRoutes() },
      { ...masterContactRoutes() },
      { ...claimRoutes() },
      { ...userRoutes() },
      { ...memberRoutes() },
      { ...crmRoutes() },
      { ...configurationRoutes() },

      {
        path: '',
        element: <Navigate to='/app' replace />,
      },
      { path: '*', element: <Navigate to='/404' replace /> },
    ],
  },
  {
    path: '*',
    children: [
      { path: '404', element: <PageNotFound /> },
      { path: '*', element: <Navigate to='/404' replace /> },
    ],
  },
  {
    path: '',
    element: isLoggedIn ? <Navigate to='/app' replace /> : <Navigate to='/auth/signin' replace />,
  },
  { path: '*', element: <Navigate to='/404' replace /> },
]

const AppRoutes: FC = () => {
  const isAuthenticated = true
  return <Suspense fallback={<LoadingScreen />}>{useRoutes(rootRoutes(isAuthenticated))}</Suspense>
}

export default AppRoutes
