// components

// css reset
import CssBaseline from '@mui/material/CssBaseline'

// app provider
import MuiThemeProvider from './plugins/@mui/components/@mui-theme.provider'

// context provider
import ReduxProvider from './plugins/redux/provider'
import AppContextProvider from './modules/app/contexts/app.context'

// routes
import AppRoutes from './app-routes'

// toaster
import { Toaster } from 'react-hot-toast'

const AppWrapper = () => {
  return (
    <ReduxProvider>
      <MuiThemeProvider>
        <CssBaseline />
        <AppContextProvider>
          <AppRoutes />
          {/* toaster */}
          <Toaster />
        </AppContextProvider>
      </MuiThemeProvider>
    </ReduxProvider>
  )
}

export default AppWrapper
