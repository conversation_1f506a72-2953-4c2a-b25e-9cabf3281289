import { FC, ReactNode, useEffect, useMemo, useState } from 'react'

// @mui
import { createTheme } from '@/theme'
import CssBaseline from '@mui/material/CssBaseline'
import { Theme } from '@mui/material/styles'
import { ThemeProvider } from '@mui/material/styles'
import { useApp } from '@/modules/app/hooks'

const PREFERRED_MODE_KEY = 'preferred-dark-mode'

const MuiThemeProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const mediaQuery = window?.matchMedia('(prefers-color-scheme: dark)')

  const { app_isDarkMode } = useApp()

  const preferredMode = Boolean(window?.localStorage.getItem(PREFERRED_MODE_KEY))

  const [_, setIsDarkMode] = useState(false)

  useEffect(() => {
    if (preferredMode) {
      setIsDarkMode(true)
    }

    if (mediaQuery?.matches) {
      setIsDarkMode(true)
    } else {
      setIsDarkMode(false)
    }

    const handleChange = (e: MediaQueryListEventMap['change']): void => {
      setIsDarkMode(e.matches)
    }

    mediaQuery.addEventListener('change', handleChange)

    // Clean up the listener on unmount
    return () => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }, [mediaQuery?.matches, preferredMode])
 
  const theme = useMemo<Theme>(() => createTheme(app_isDarkMode), [app_isDarkMode])

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  )
}

export default MuiThemeProvider
