import { Components } from '@mui/material'

const components: Components = {
  MuiButton: {
    defaultProps: {
      disableRipple: true,
      size: 'small',
    },
    styleOverrides: {
      root: {
        textTransform: 'unset',
        borderRadius: '6px',
      },
    },
  },

  MuiFab: {
    defaultProps: {
      disableRipple: true,
      size: 'small',
    },
  },

  MuiButtonBase: {
    defaultProps: {
      disableRipple: true,
    },
  },

  MuiTextField: {
    defaultProps: {
      size: 'small',
    },
    styleOverrides: {
      root: {
        '& .MuiOutlinedInput-root': {
          borderRadius: '10px',
        },
      },
    },
  },
  MuiFormControl: {
    defaultProps: {
      size: 'small',
    },
    styleOverrides: {
      root: {
        '& .MuiOutlinedInput-root': {
          borderRadius: '10px',
        },
      },
    },
  },

  MuiMenuItem: {
    defaultProps: {
      disableRipple: true,
    },
  },

  MuiListItemButton: {
    defaultProps: {
      disableRipple: true,
    },
  },

  MuiDialogTitle: {
    styleOverrides: {
      root: {
        fontSize: '0.95rem',
        fontWeight: 500,
      },
    },
  },
}

export default components
