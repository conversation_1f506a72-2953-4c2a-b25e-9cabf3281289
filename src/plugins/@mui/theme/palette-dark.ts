import { alpha, PaletteColorOptions, PaletteOptions } from '@mui/material'
import { grey } from '@mui/material/colors'
import paletteBase from './palette-base'

const paletteDark: PaletteOptions = {
  mode: 'dark',
  background: {
    default: '#151821',
    paper: '#1a1f2d',
  },
  text: {
    primary: '#f4f6ff',
    secondary: '#b6bbe0',
    disabled: grey[400],
  },
  divider: alpha('#fff', 0.02),
  primary: {
    ...paletteBase.primary,
    dark: '#274171',
  } as PaletteColorOptions,
  secondary: {
    ...paletteBase.secondary,
  } as PaletteColorOptions,
}

export default paletteDark
