import { alpha, PaletteColorOptions, PaletteOptions } from '@mui/material/styles'
import { grey } from '@mui/material/colors'
import paletteBase from './palette-base'

const palette: PaletteOptions = {
  mode: 'light',
  background: {
    default: '#f1f3f8',
    paper: '#ffffff',
  },
  text: {
    primary: grey[800],
    secondary: grey[600],
    disabled: grey[500],
  },
  divider: alpha('#000', 0.04),
  primary: {
    ...paletteBase.primary,
  } as PaletteColorOptions,
  secondary: {
    ...paletteBase.secondary,
  } as PaletteColorOptions,
}

export default palette
