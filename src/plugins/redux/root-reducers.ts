import { combineReducers } from '@reduxjs/toolkit'

// slices
import { appSlice } from '@/modules/app/slices'
import { authSlice } from '@/modules/auth/slices/auth.slice'
import { masterContactSlice } from '@/modules/master-contact/slices/master-contact.slice'
import { userSlice } from '@/modules/user/slices'
import { memberSlice } from '@/modules/member/slices'
import { claimSlice } from '@/modules/claim/slices'

const rootReducer = combineReducers({
  [appSlice.name]: appSlice.reducer,
  [authSlice.name]: authSlice.reducer,
  [masterContactSlice.name]: masterContactSlice.reducer,
  [userSlice.name]: userSlice.reducer,
  [memberSlice.name]: memberSlice.reducer,
  [claimSlice.name]: claimSlice.reducer,
})

export default rootReducer
