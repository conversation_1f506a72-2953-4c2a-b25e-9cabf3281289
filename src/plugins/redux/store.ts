// redux tookit
import { configureStore, createListenerMiddleware } from '@reduxjs/toolkit'

// redux persist
import { persistStore, persistReducer, PersistConfig } from 'redux-persist'

// persist storag
import storage from 'redux-persist/lib/storage'

// root reducer
import rootReducer from './root-reducers'
import { authSlice } from '@/modules/auth/slices'
import { appSlice } from '@/modules/app/slices'

// persist config
const persistConfig: PersistConfig<RootState> = {
  key: 'app_v0.0.3',
  storage,
  whitelist: [authSlice.name, appSlice.name],
}

// make persisted store
const persistedReducer = persistReducer(persistConfig, rootReducer)

// listener middleware
export const listenerMiddleware = createListenerMiddleware()

// root store
const store = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware => {
    const middlewares = getDefaultMiddleware({
      serializableCheck: false,
      immutableCheck: false,
      // serializableCheck: {
      //   ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      // },
    })
      .prepend(listenerMiddleware.middleware)
      .concat([])

    return middlewares
  },
})

// store that persisted
const persistor = persistStore(store)

// types
export type RootDispatch = typeof store.dispatch

export { store, persistor }
