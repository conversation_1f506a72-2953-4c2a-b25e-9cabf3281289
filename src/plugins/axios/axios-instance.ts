import axios, { AxiosError, AxiosResponse } from 'axios'

// configs
import { appConfig } from '@/modules/app/configs'

// store
import { store } from '../redux/store'

// toast
import toast from 'react-hot-toast'

// On request rejected
const onRequestError = (axiosError: AxiosError) => {
  return axiosError
}

// On response fulfilled
const onResponseSuccess = (axiosResponse: AxiosResponse) => {
  return axiosResponse
}

// On response rejected
const onResponseError = (axiosError: AxiosError) => {
  if (axios.isAxiosError(axiosError) && axiosError.response?.status === 401) {
    // Redirect to your login page
    window.location.href = '/auth/signin'

    // Important: Return a rejected promise to stop further processing
    // in the calling code (e.g., prevent the .then() block from executing)
    return Promise.reject(axiosError)
  }

  if (axios.isAxiosError(axiosError) && axiosError.response?.status === 422) {
    // @ts-ignore
    const message = axiosError.response?.data?.message ?? ''
    if (message) {
      toast.error(message, { duration: 3000, position: 'bottom-center' })
    }
    return Promise.reject(axiosError)
  }

  return Promise.reject(axiosError)
}

/**
 * axios instance
 */
const axiosInstance = axios.create({
  baseURL: appConfig.apiBaseUrl,
  timeout: 120 * 60 * 1000,
})

// On request
axiosInstance.interceptors.request.use(
  async config => {
    try {
      const authToken = store.getState().auth?.token
      if (authToken) {
        config.headers['Authorization'] = `Bearer ${authToken}`
      }
    } catch (e) {
      console.log(e)
    }
    return config
  },
  error => {
    return Promise.reject(onRequestError(error))
  }
)

// on response
axiosInstance.interceptors.response.use(
  async response => {
    return onResponseSuccess(response)
  },
  async error => {
    return onResponseError(error)
  }
)

export default axiosInstance
