:root {
  --color-background: #6d6d6d;
  --color-scrollbar: rgb(255, 255, 255, 0.4);
  --color-scrollbar-hover: #59595a;
}

::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
::-webkit-scrollbar-track,
::-webkit-scrollbar-corner {
  background: var(--color-background);
  border-radius: 10px;
}
::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar);
  border-radius: 10px;
}
::-webkit-scrollbar-track,
::-webkit-scrollbar-corner {
  background: var(--color-background);
  border-radius: 10px;
}

/* swal2 */
div:where(.swal2-container) {
  z-index: 6001 !important;
}

div:where(.swal2-container) h2:where(.swal2-title) {
  position: relative;
  max-width: 100%;
  margin: 0;
  padding: 0 !important;
  color: inherit;
  font-size: 1.2rem !important;
  font-weight: 600;
  text-align: center;
  text-transform: none;
  word-wrap: break-word;
  cursor: initial;
  font-family: Jost !important;
}
div:where(.swal2-container) div:where(.swal2-popup) {
  width: var(--swal2-width);
  max-width: 100%;
  padding: 0 3rem !important;
  border: var(--swal2-border);
  border-radius: var(--swal2-border-radius);
  background: var(--swal2-background);
  color: var(--swal2-color);
  font-family: inherit;
  font-size: 0.9rem;
  width: 360px !important;
}

.swal2-actions button {
  min-width: 72px !important;
  font-weight: 600 !important;
  font-size: 0.8rem !important;
  line-height: 1.1rem;
  border-radius: 0.4rem;
}

div:where(.swal2-container) div:where(.swal2-popup) {
  border-radius: 20px !important;
  padding: 1.2rem 1rem !important;
  border: none !important;
}

div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm) {
  background-color: #3e6fff !important;
}

div:where(.swal2-container) div:where(.swal2-html-container) {
  font-size: 0.9rem !important;
  padding-top: 0.4rem !important;
}

.swal2-icon {
  transform: scale(0.75) !important;
  margin-top: -2px !important;
}
