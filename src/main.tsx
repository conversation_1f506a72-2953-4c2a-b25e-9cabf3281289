import { createRoot } from 'react-dom/client'

// fonts
import '@fontsource/jost/300.css'
import '@fontsource/jost/300-italic.css'
import '@fontsource/jost/400-italic.css'
import '@fontsource/jost/500.css'
import '@fontsource/jost/500-italic.css'
import '@fontsource/jost/600.css'
import '@fontsource/jost/600-italic.css'
import '@fontsource/jost/700.css'
import '@fontsource/jost/700-italic.css'
import '@fontsource/jost/800.css'
import '@fontsource/jost/800-italic.css'
import './styles.css'

// app wrapper
import AppWrapper from './app-wrapper'

// react router
import { BrowserRouter } from 'react-router-dom'

createRoot(document.getElementById('root')!).render(
  <BrowserRouter>
    <AppWrapper />
  </BrowserRouter>
)
