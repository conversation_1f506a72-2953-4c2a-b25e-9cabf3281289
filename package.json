{"name": "aksi-crm-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,css}\""}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fontsource/jost": "^5.2.6", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-data-grid": "^8.9.1", "@mui/x-date-pickers": "^8.9.0", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.11.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.23.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.61.0", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "react-router-dom": "^7.7.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "simplebar-react": "^3.3.2", "sweetalert2": "^11.22.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react-swc": "^3.10.2", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4"}}